{"name": "ralicon-batteries", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "openapi-gen": "openapi-ts", "build:family": "cross-env VITE_APP_TYPE=family vite build", "build:partner": "cross-env VITE_APP_TYPE=partner vite build", "cap:sync:family": "cross-env VITE_APP_TYPE=family npm run build:family && npx cap sync", "cap:sync:partner": "cross-env VITE_APP_TYPE=partner npm run build:partner && npx cap sync", "cap:build:android:family": "npm run cap:sync:family && cd android && ./gradlew assembleFamilyDebug assembleFamilyDebugUnitTest", "cap:build:android:partner": "npm run cap:sync:partner && cd android && ./gradlew assemblePartnerDebug assemblePartnerDebugUnitTest", "cap:build:android": "run-s cap:build:android:*", "cap:bundle:android:family": "npm run cap:sync:family && cd android && ./gradlew bundleFamilyRelease", "cap:bundle:android:partner": "npm run cap:sync:partner && cd android && ./gradlew bundlePartnerRelease", "cap:bundle:android": "run-s cap:bundle:android:*", "cap:all:android": "run-s cap:build:android cap:bundle:android", "cap:open:android:family": "cross-env VITE_APP_TYPE=family npm run cap:sync:family && npx cap open android", "cap:open:android:partner": "cross-env VITE_APP_TYPE=partner npm run cap:sync:partner && npx cap open android", "cap:icons:android:family": "capacitor-assets generate --android --assetPath src/assets/capacitor/family && mv android/app/src/main/res/mipmap-* android/app/src/family/res/ && mv android/app/src/main/res/drawable-* android/app/src/family/res/", "cap:icons:android:partner": "capacitor-assets generate --android --assetPath src/assets/capacitor/partner && mv android/app/src/main/res/mipmap-* android/app/src/partner/res/ && mv android/app/src/main/res/drawable-* android/app/src/partner/res/", "cap:icons:ios:family": "capacitor-assets generate --ios --assetPath src/assets/capacitor/family && cp -vR ios/App/App/Assets.xcassets/AppIcon.appiconset ios/App/App/Assets.xcassets/FamilyAppIcon.appiconset", "cap:icons:ios:partner": "capacitor-assets generate --ios --assetPath src/assets/capacitor/partner && cp -vR ios/App/App/Assets.xcassets/AppIcon.appiconset ios/App/App/Assets.xcassets/PartnerAppIcon.appiconset", "cap:icons": "run-s cap:icons:*", "cap:run:ios:family": "cross-env VITE_APP_TYPE=family npm run cap:sync:family && npx cap open ios", "cap:run:ios:partner": "cross-env VITE_APP_TYPE=partner npm run cap:sync:partner && npx cap open ios"}, "dependencies": {"@capacitor/android": "^7.0.1", "@capacitor/core": "^7.0.1", "@capacitor/ios": "^7.0.1", "@hey-api/client-axios": "^0.5.0", "@tanstack/vue-query": "^5.64.2", "@tanstack/vue-query-devtools": "^5.64.2", "@types/lodash": "^4.17.16", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^12.4.0", "axios": "^1.7.9", "lodash": "^4.17.21", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.2.0", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "^7.0.1", "@hey-api/openapi-ts": "^0.62.1", "@rushstack/eslint-patch": "^1.10.4", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^22.10.5", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.2.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.17.0", "eslint-plugin-vue": "^9.32.0", "jsdom": "^25.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vite": "^6.0.5", "vitest": "^2.1.8", "vue-tsc": "^2.2.0"}}