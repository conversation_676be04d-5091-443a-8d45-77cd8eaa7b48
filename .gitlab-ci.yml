# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/pipeline/#customization
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Container Scanning customization: https://docs.gitlab.com/ee/user/application_security/container_scanning/#customizing-the-container-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence
stages:
  - test
  - build webapp
  - deploy webapp
  - prepare family
  - build family
  - prepare partner
  - build partner
  - deploy
# sast:
#   stage: test
#   rules:
#     - exists:
#       - .env
# include:
# - template: Security/SAST.gitlab-ci.yml
  
build webapp:
  image: node:22-alpine # Specify the exact image tag instead of :latest
  stage: build webapp
  rules:
  - exists:
    - .env
  variables:
    npm_config_cache: "$CI_PROJECT_DIR/.npm"  # Move npm cache to inside the CI directory
  script:
    - echo "CI/CD Pipeline initialted on $CI_COMMIT_REF_NAME"
    - npm ci --progress=false
    - npm run build-only
  cache:
    key:
      files:
        - package-lock.json
    paths:
        - .npm # Cache npm cache directory across builds to save precious time
  artifacts:
    expire_in: 3 days
    paths:
      - dist

deploy webapp:
  image: node:22-alpine
  stage: deploy webapp
  environment:
    name: ci/$CI_COMMIT_BRANCH # Use branch name for environment to allow re-use of ci file over multiple branches
  rules:
    - if: '$CLOUDFLARE_ACCOUNT_ID != null'
    - if: '$CLOUDFLARE_API_TOKEN != null'
    - if: '$CLOUDFLARE_PROJECT_NAME != null'
  script:
    - echo "Deploying to Cloudflare Pages using Wrangler."
    - npm install wrangler --save-dev
    - npx wrangler pages deploy dist --project-name=$CLOUDFLARE_PROJECT_NAME

prepare family:
  image: node:22-alpine
  stage: prepare family
  rules:
    - exists:
      - .env
  script:
    - npm ci --progress=false
    - npm run cap:sync:family
  artifacts:
    expire_in: 30 minutes
    paths:
      - android
      - node_modules
  
build family:
  image: mobiledevops/android-sdk-image:34.0.0
  stage: build family
  tags:
    - x64
  rules:
    - exists:
      - .env
  script:
    - cd android && ./gradlew clean bundleFamilyRelease --scan
  artifacts:
    expire_in: 3 days
    paths:
      - android/app/build/outputs
  
prepare partner:
  image: node:22-alpine
  stage: prepare partner
  rules:
    - exists:
      - .env
  script:
    - npm ci --progress=false
    - npm run cap:sync:partner
  artifacts:
    expire_in: 30 minutes
    paths:
      - android
      - node_modules
  
build partner:
  image: mobiledevops/android-sdk-image:34.0.0
  stage: build partner
  tags:
    - x64
  rules:
    - exists:
      - .env
  script:
    - cd android && ./gradlew clean bundlePartnerRelease --scan
  artifacts:
    expire_in: 3 days
    paths:
      - android/app/build/outputs
  
## References
# https://dev.to/drakulavich/gitlab-ci-cache-and-artifacts-explained-by-example-2opi
  