# R A Batteries

Web application to manage warranty status of customers and dealers.

## Workflow

### New Customer:
- Sign-up to the app using mobile OTP verification.
- Register equipment using the serial number.
- The backend will provide product details based on the serial number.
- If the serial number is not in the database, the web app will show an error message.
- If the product is already registered with someone else, display a message indicating that the product is already registered.
- If the serial number is valid and not registered, register the product and provide warranty details.

### Existing Customer:
- Login, if not already logged in, using OTP verification.
- Show the dashboard with the following features:
    - Widgets: 
        - Total products registered
        - Expiring warranty in X days
    - Buttons: 
        - Register Product
        - Buy Product
        - Warranty Claim
        - Buy Extended Warranty
- Contact Page for customer support and inquiries.

## Additional Features:
- Admin Panel for managing products, customers, and dealers.
- Notification system to alert customers about expiring warranties.
- Reporting tools for generating warranty and registration reports.
- Multi-language support for a wider audience.

## Database Design

### Tables:

#### Users
- `user_id` (Primary Key)
- `name`
- `email`
- `phone_number`
- `password_hash`
- `user_type` (e.g., 'customer', 'staff', 'dealer')
- `created_at`
- `updated_at`

#### Products
- `product_id` (Primary Key)
- `serial_number` (Unique)
- `name`
- `description`
- `price`
- `created_at`
- `updated_at`

#### Registrations
- `registration_id` (Primary Key)
- `user_id` (Foreign Key)
- `product_id` (Foreign Key)
- `registration_date`
- `expiration_date`

#### Notifications
- `notification_id` (Primary Key)
- `user_id` (Foreign Key)
- `message`
- `channel` (SMS, Email, WhatsApp, Push Notification)
- `sent_at`

#### Reports
- `report_id` (Primary Key)
- `user_id` (Foreign Key)
- `report_type`
- `generated_at`

#### Extended_Warranties
- `extended_warranty_id` (Primary Key)
- `registration_id` (Foreign Key)
- `purchase_date`
- `expiration_date`
- `price`

### Future E-commerce Features:

#### Orders
- `order_id` (Primary Key)
- `user_id` (Foreign Key)
- `order_date`
- `total_amount`
- `status`

#### Order_Items
- `order_item_id` (Primary Key)
- `order_id` (Foreign Key)
- `product_id` (Foreign Key)
- `quantity`
- `price`

#### Payments
- `payment_id` (Primary Key)
- `order_id` (Foreign Key)
- `payment_date`
- `amount`
- `payment_method`
- `status`

### Relationships:
- Each user can register multiple products.
- Each product can be registered by only one user.
- Admins manage products, customers, and dealers.
- Notifications are sent to users.
- Reports are generated for users.
- Orders and payments are linked to users for future e-commerce features.
- Extended warranties are linked to product registrations.

## API-Based Backend (Draft)

The backend of the application is built using a RESTful API architecture. Below are the key endpoints and their descriptions:

### Endpoints:

#### Authentication
- `POST /api/auth/signup`: Sign up a new user.
- `POST /api/auth/login`: Log in an existing user.
- `POST /api/auth/verify-otp`: Verify OTP for user authentication.

#### Users
- `GET /api/users/:user_id`: Get user details.
- `PUT /api/users/:user_id`: Update user details.
- `DELETE /api/users/:user_id`: Delete a user.

#### Products
- `GET /api/products`: Get a list of all products.
- `GET /api/products/:serial_number`: Get product details by serial number.
- `POST /api/products`: Add a new product (Admin only).
- `PUT /api/products/:product_id`: Update product details (Admin only).
- `DELETE /api/products/:product_id`: Delete a product (Admin only).

#### Registrations
- `POST /api/registrations`: Register a product.
- `GET /api/registrations/:user_id`: Get all registrations for a user.
- `GET /api/registrations/:registration_id`: Get registration details by ID.

#### Extended Warranties
- `POST /api/extended-warranties`: Purchase an extended warranty.
- `GET /api/extended-warranties/:user_id`: Get all extended warranties for a user.
- `GET /api/extended-warranties/:extended_warranty_id`: Get extended warranty details by ID.

#### Notifications
- `GET /api/notifications/:user_id`: Get all notifications for a user.
- `POST /api/notifications`: Send a notification (Admin only).

#### Reports
- `GET /api/reports/:user_id`: Get all reports for a user.
- `POST /api/reports`: Generate a new report (Admin only).

#### Orders
- `POST /api/orders`: Create a new order.
- `GET /api/orders/:user_id`: Get all orders for a user.
- `GET /api/orders/:order_id`: Get order details by ID.

#### Payments
- `POST /api/payments`: Process a payment.
- `GET /api/payments/:user_id`: Get all payments for a user.
- `GET /api/payments/:payment_id`: Get payment details by ID.

### Authentication:
- All endpoints require authentication via JWT tokens.
- Admin endpoints require additional admin privileges.

### Error Handling:
- Standardized error responses with appropriate HTTP status codes.
- Detailed error messages for debugging and user feedback.

### Rate Limiting:
- Implement rate limiting to prevent abuse of the API.

### Documentation:
- Comprehensive API documentation using tools like Swagger or Postman.

### Security:
- Secure endpoints with HTTPS.
- Implement input validation and sanitization to prevent SQL injection and other attacks.