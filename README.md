# Ralicoan Family

Universal webapp for Ralicoan Endusers.

## Introduction

Ralicoan Family is a multi-platform application designed for Ralicoan end-users and partners. The project consists of multiple app variants (Family, Partner, and Team) sharing a common codebase but with different configurations and features.

## Features

- [ ] Customer Sign-up
- [ ] Product Warranty Registration
- [ ] Partner Search based on Pin code. (Map pin code to city and state and list all the partners sorted by city)

## Technology Stack

- VueJS Frontend
- CapacitorJS for mobile apps
- Frontend Unit Tests
- Automates SAST through `.gitlab-ci.yml`
- CI/CD to frontend server
- CI/CD for mobile apps

## Installation

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Android Studio (for Android development)
- Xcode (for iOS development)
- Capacitor CLI

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Copy the environment file and configure it:
   ```bash
   cp .env.example .env.local
   ```
4. Set up the keystore for Android:
   - Create a `keystore.properties` file in the `android` directory with the following content:
     ```
     storeFile=path/to/your/keystore.jks
     storePassword=your_store_password
     keyAlias=your_key_alias
     keyPassword=your_key_password
     ```

## Development

### Running the App

- For web development:
  ```bash
  npm run dev
  ```

- For Android development:
  ```bash
  # For Family app
  npm run cap:open:android:family

  # For Partner app
  npm run cap:open:android:partner
  ```

- For iOS development:
  ```bash
  # For Family app
  npm run cap:run:ios:family

  # For Partner app
  npm run cap:run:ios:partner
  ```

### Project Structure

- `src/` - Main source code
- `android/` - Android platform-specific code
- `ios/` - iOS platform-specific code
- `dist/` - Build output

## Building

### For Android

- For release, run the `cap:bundle:android` command. This will generate the `aab` file in the `android/app/build/outputs/bundle/release` directory, under the respective flavor directory.
- For debug, run the `cap:build:android` command. This will generate the `apk` file in the `android/app/build/outputs/apk/debug` directory, under the respective flavor directory.
- Setup the `keystore.properties` file in the `android` directory. This file contains the keystore details for signing the app bundle.

### For iOS

- For development, run the `cap:run:ios:family` or `cap:run:ios:partner` command to open the project in Xcode.
- In Xcode, select the target device and build the app.
- For distribution, use Xcode to archive the app and upload it to the App Store.

## Testing

### Running Tests

```bash
# Run unit tests
npm run test:unit

# Run tests with coverage
npm run test:unit -- --coverage
```

### Writing Tests

- Tests are located in the `src/__tests__` directory
- Use Vitest for unit testing
- Follow the naming convention: `*.spec.ts` for test files

## API Integration

The project uses OpenAPI for API integration:

- The OpenAPI specification is linked from the backend project (`openapi.yaml`)
- API client code is generated using `@hey-api/openapi-ts`
- To update the API client after backend changes:
  ```bash
  npm run openapi-gen
  ```

## Deployment

### CI/CD Pipeline

The project uses GitLab CI/CD for automated deployment:

- The pipeline is defined in `.gitlab-ci.yml`
- SAST (Static Application Security Testing) is automatically run on each commit
- Deployments are triggered automatically on specific branches

### Environments

- **Development**: Automatically deployed from the `develop` branch
- **Staging**: Automatically deployed from the `staging` branch
- **Production**: Manually triggered from the `main` branch

## App Variants

The project supports multiple app variants:

- **Family**: For end-users (customers)
- **Partner**: For service partners and dealers
- **Team**: For internal team members

Each variant has its own:
- Application ID
- App icons and splash screens
- Environment-specific configurations

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Ensure you have the latest dependencies: `npm install`
   - Check that your environment variables are correctly set
   - Verify that the keystore.properties file is correctly configured

2. **Capacitor Sync Issues**:
   - Run `npx cap sync` to ensure platform files are up to date
   - Check for any errors in the capacitor.config.ts file

3. **API Connection Issues**:
   - Verify that the backend server is running
   - Check that the API URL in your .env.local file is correct

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature-name`
5. Submit a pull request

### Coding Standards

- Follow the ESLint configuration
- Run `npm run lint` and `npm run format` before committing
- Write tests for new features