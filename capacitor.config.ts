import type { CapacitorConfig } from '@capacitor/cli';

type AppType = 'family' | 'partner';

const getAppType = () => {
	return process.env.VITE_APP_TYPE || ('family' as AppType);
};

const getAppConfig = () => {
	const appType = getAppType();
	const configs: Record<AppType, CapacitorConfig> = {
		family: {
			appId: 'in.rabatteries.app.family',
			appName: 'Ralicoan Family',
			android: {
				flavor: 'family',
			},
			ios: {
				scheme: 'Ralicoan Family'
			},
		},
		partner: {
			appId: 'in.rabatteries.app.partner',
			appName: 'Ralicoan Partner',
			android: {
				flavor: 'partner',
			},
			ios: {
				scheme: 'Ralicoan Partner',
			}
		},
	};

	return configs[appType] || configs.family;
};

const appConfig = getAppConfig();

const config: CapacitorConfig = {
	...appConfig,
	webDir: 'dist',
	server: {
		androidScheme: 'https',
	},
	plugins: {
		// SplashScreen: {
		// 	launchShowDuration: 3000,
		// 	backgroundColor: '#FFFFFF',
		// },
	},
};

export default config;
