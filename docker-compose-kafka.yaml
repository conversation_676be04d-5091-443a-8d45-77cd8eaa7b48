services:
  zookeeper-1:
    image: confluentinc/cp-zookeeper:7.4.4
    environment:
      Z<PERSON><PERSON>EEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - 22181:2181

  # zookeeper-2:
  #   image: confluentinc/cp-zookeeper:7.4.4
  #   environment:
  #     Z<PERSON><PERSON>EEPER_CLIENT_PORT: 2181
  #     ZOOKEEPER_TICK_TIME: 2000
  #   ports:
  #     - 32181:2181
  
  kafka-1:
    image: confluentinc/cp-kafka:7.4.4
    depends_on:
      - zookeeper-1
#      - zookeeper-2

    ports:
      - 29092:29092
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-1:2181 #,zookeeper-2:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-1:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
  # kafka-2:
  #   image: confluentinc/cp-kafka:7.4.4
  #   depends_on:
  #     - zookeeper-1
  #     - zookeeper-2
  #   ports:
  #     - 39092:39092
  #   environment:
  #     KAFKA_BROKER_ID: 2
  #     KAFKA_ZOOKEEPER_CONNECT: zookeeper-1:2181,zookeeper-2:2181
  #     KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-2:9092,PLAINTEXT_HOST://localhost:39092
  #     KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
  #     KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
  #     KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1