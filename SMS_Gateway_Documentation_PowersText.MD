# [WIP] SMS Gateway API Documentation
**PowersText**

## Send

### Endpoint:
```
https://sms.akaal.biz/http-tokenkeyapi.php?authentic-key={env.apiToken}&senderid={env.smsSenderId}&route={env.smsRoute}&number={phone-number}&message={message}&templateid={env.smsSignUpTemplateId}
```

### Parameters:
| Parameter | Type | Description |
| --- | --- | --- |
| authentic-key | String | The authentication key for API access. Replace xxxxxxxxxx with your key. |
| senderid | String | The sender ID for the SMS. Must be pre-approved. |
| route | Integer | The route for the message. Use 1 for transactional messages. |
| number | String | The recipient's mobile number. Use international format if required. |
| message | String | The text message to be sent. |
| templateid | String | The template ID for the SMS. Mandatory for transactional routes |

### Return Status 
The API returns a JSON response.

#### Success Response: 
```
{
    "Status": "Success",
    "Code": "000",
    "Message-Id": "xxxxxxxxxxxx",
    "Description": "Message has been Sent Successfully"
}
```

#### Error Response:
```
{
    "Status": "Failed",
    "Code": "000", /* Error code */
    "Message-Id": "xxxxxxxxxxxx",
    "Description": "Error description"
}
```

## Check Delivery Status

### Endpoint:
```
https://sms.akaal.biz/http-dlr.php?authentic-key={env.apiToken}&msg_id={message-id}
```

### Parameters:
| Parameter | Type | Description |
| --- | --- | --- |
| authentic-key | String | The authentication key for API access. Replace xxxxxxxxxx with your key. |
| msg_id | String | The message ID received when sending sms. |

### Return Status
The API returns a JSON response.

#### Success Response:
```
{
    "Status": "Success",
    "Code": "000",
    "Total Balance ": "10"
}
```

#### Error Response:
```
{
    "Status": "Failed",
    "Code":"001", /* Error code */
    "Description":"Provide the valid route ID"
}
```


## Check Balance

### Endpoint:
```
https://sms.akaal.biz/http-credit.php?authentic-key={env.apiToken}&route_id={env.smsRoute}
```

### Parameters:
| Parameter | Type | Description |
| --- | --- | --- |
| authentic-key | String | The authentication key for API access. Replace xxxxxxxxxx with your key. |
| route | Integer | The route for the message. Use 1 for transactional messages. |

### Return Status
The API returns a JSON response.

#### Success Response:
```
{
    "Status": "Success",
    "Code": "000",
    "Total Balance ": "10"
}
```

#### Error Response:
```
{
    "Status": "Failed",
    "Code":"001", /* Error code */
    "Description":"Provide the valid route ID"
}
```


## TOTO:
- [ ] Add endpoint to check delivery status
- [ ] Add endpoint to check DND status of the number