// init api client
import { client } from './api';

// init vue
import { createApp } from 'vue';
import './style.css';
import App from './App.vue';
import router from './routes';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

// pinia
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

// api client
import { VueQueryPlugin, type VueQueryPluginOptions } from '@tanstack/vue-query';
import { AxiosError } from 'axios';

createApp(App)
	.use(pinia)
	.use(router)
	.use(VueQueryPlugin, {
		enableDevtoolsV6Plugin: true,
		queryClientConfig: {
			defaultOptions: {
				queries: {
					retry: function (failureCount, error) {
						if (failureCount >= 3) {
							return false;
						}
						if (
							error instanceof AxiosError &&
							[401, 403, 404, 422, 500].includes(error.response?.status ?? 0)
						) {
							return false;
						}

						if(typeof error === 'string') {
							return false;
						}

						return true;
					},
				},
			},
		},
	} satisfies VueQueryPluginOptions)
	.mount('#app');
