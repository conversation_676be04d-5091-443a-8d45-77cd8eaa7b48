// This file is auto-generated by @hey-api/openapi-ts

export type User = {
    id: number;
    name?: string | null;
    email?: string | null;
    phone_number?: string | null;
    dealership_code?: string | null;
    dealership_activated_at?: string | null;
    email_verified_at?: string | null;
    created_at?: string;
    updated_at?: string;
    stocks?: Array<Stock>;
};

export type Product = {
    id: number;
    name: string;
    price: number;
    description: string;
    specifications?: {
        [key: string]: unknown;
    };
    warranty?: WarrantySpec | null;
    status: 'active' | 'inactive';
    created_at?: string;
    updated_at?: string;
    stocks?: Array<Stock>;
};

export type Stock = {
    id: number;
    serial_number: string;
    product_id: number;
    warrantee_id?: number | null;
    warranty_start?: string | null;
    warranty_end?: string | null;
    warranty?: WarrantySpec | null;
    order_item_id?: number | null;
    created_at: string;
    updated_at: string;
    deleted_at?: string;
    product?: Product;
    warrantee?: null | User;
    order_item?: Array<OrderItem>;
};

export type Order = {
    id: number;
    user_id: number;
    order_placed_at: string;
    address: string;
    pincode: string;
    phone_number: string;
    email?: string | null;
    bill_number?: string | null;
    created_at: string;
    updated_at: string;
    deleted_at: string;
    order_items?: Array<OrderItem>;
};

export type OrderItem = {
    id: number;
    product_id: number;
    order_id: number;
    quantity: number;
    created_at: string;
    updated_at: string;
    product?: Product;
    stocks?: Array<Stock>;
};

export type Complaint = {
    id: number;
    /**
     * The complaint text
     */
    complaint: string;
    stock_id: number;
    user_id: number;
    raised_by_id: number;
    assignee_id?: number | null;
    resolution?: string | null;
    resolved_at?: string | null;
    created_at: string;
    updated_at: string;
    deleted_at?: string | null;
    stock?: Stock;
    user?: UserMinimal;
    assignee?: null | UserMinimal;
    raisedBy?: UserMinimal;
    updates?: Array<ComplaintUpdate>;
};

export type ComplaintUpdate = {
    /**
     * The type of update
     */
    type: 'raised' | 'assigned' | 'resolved';
    /**
     * When the update occurred
     */
    time: string;
};

export type UserMinimal = {
    id: number;
    name: string;
};

export type WarrantySpec = {
    /**
     * Description of the warranty
     */
    description: string;
    /**
     * Duration of warranty (in months)
     */
    duration: number;
    /**
     * Extension of warranty (in months)
     */
    extension: number;
    /**
     * Leeway of warranty for sales (in months)
     */
    leeway: number;
};

export type Acceptance = {
    id: number;
    user_id?: number;
    ip_address?: string;
    user_agent?: string;
    document?: string;
    document_version?: string;
    created_at?: string;
    updated_at?: string;
};

export type Agreeable = {
    id: string;
    content?: string;
    label?: string;
    created_at?: string;
};

export type _Error = {
    message?: string;
    errors?: {
        [key: string]: unknown;
    };
};

export type ValidationError = {
    message?: string;
    errors?: {
        [key: string]: Array<string>;
    };
};

export type NotFoundError = {
    message?: string;
};

export type UnauthorizedError = {
    message?: string;
};

export type ForbiddenError = {
    message?: string;
};

export type LoginUserData = {
    body: {
        email: string;
        password: string;
    };
    path?: never;
    query?: never;
    url: '/api/auth/login';
};

export type LoginUserErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Validation failed
     */
    422: ValidationError;
};

export type LoginUserError = LoginUserErrors[keyof LoginUserErrors];

export type LoginUserResponses = {
    /**
     * Login successful
     */
    200: {
        token?: string;
    };
};

export type LoginUserResponse = LoginUserResponses[keyof LoginUserResponses];

export type LoginWithOtpData = {
    body: {
        phone_number: string;
        otp: string;
        app_type: string;
    };
    path?: never;
    query?: never;
    url: '/api/auth/login-otp';
};

export type LoginWithOtpErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Validation failed
     */
    422: ValidationError;
};

export type LoginWithOtpError = LoginWithOtpErrors[keyof LoginWithOtpErrors];

export type LoginWithOtpResponses = {
    /**
     * Login successful
     */
    200: {
        access_token: string;
        user: User;
    };
};

export type LoginWithOtpResponse = LoginWithOtpResponses[keyof LoginWithOtpResponses];

export type SendOtpData = {
    body: {
        phone_number: string;
        [key: string]: unknown | string;
    };
    path?: never;
    query?: never;
    url: '/api/auth/send-otp';
};

export type SendOtpErrors = {
    /**
     * Validation failed
     */
    422: ValidationError;
};

export type SendOtpError = SendOtpErrors[keyof SendOtpErrors];

export type SendOtpResponses = {
    /**
     * OTP sent successfully
     */
    200: unknown;
};

export type LogoutUserData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/auth/logout';
};

export type LogoutUserErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
};

export type LogoutUserError = LogoutUserErrors[keyof LogoutUserErrors];

export type LogoutUserResponses = {
    /**
     * Logged out successfully
     */
    200: unknown;
};

export type ListProductsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/products';
};

export type ListProductsResponses = {
    /**
     * List of products
     */
    200: Array<Product>;
};

export type ListProductsResponse = ListProductsResponses[keyof ListProductsResponses];

export type GetProductData = {
    body?: never;
    path: {
        id: number;
    };
    query?: never;
    url: '/api/products/{id}';
};

export type GetProductErrors = {
    /**
     * Resource not found
     */
    404: NotFoundError;
};

export type GetProductError = GetProductErrors[keyof GetProductErrors];

export type GetProductResponses = {
    /**
     * Product details
     */
    200: Product;
};

export type GetProductResponse = GetProductResponses[keyof GetProductResponses];

export type ListComplaintsData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Filter by resolution status
         */
        resolved?: boolean;
        /**
         * Filter by stock ID
         */
        stock_id?: number;
    };
    url: '/api/complaints';
};

export type ListComplaintsErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
};

export type ListComplaintsError = ListComplaintsErrors[keyof ListComplaintsErrors];

export type ListComplaintsResponses = {
    /**
     * List of complaints
     */
    200: {
        data: Array<Complaint>;
        links?: {
            [key: string]: unknown;
        };
        meta?: {
            [key: string]: unknown;
        };
    };
};

export type ListComplaintsResponse = ListComplaintsResponses[keyof ListComplaintsResponses];

export type CreateComplaintData = {
    body: {
        complaint: string;
        stock_id: number;
        user_id: number;
        raised_by_id?: number;
    };
    path?: never;
    query?: never;
    url: '/api/complaints';
};

export type CreateComplaintErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Validation failed
     */
    422: ValidationError;
};

export type CreateComplaintError = CreateComplaintErrors[keyof CreateComplaintErrors];

export type CreateComplaintResponses = {
    /**
     * Complaint created successfully
     */
    201: {
        data: Complaint;
    };
};

export type CreateComplaintResponse = CreateComplaintResponses[keyof CreateComplaintResponses];

export type DeleteComplaintData = {
    body?: never;
    path: {
        id: number;
    };
    query?: never;
    url: '/api/complaints/{id}';
};

export type DeleteComplaintErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
};

export type DeleteComplaintError = DeleteComplaintErrors[keyof DeleteComplaintErrors];

export type DeleteComplaintResponses = {
    /**
     * Complaint deleted successfully
     */
    204: void;
};

export type DeleteComplaintResponse = DeleteComplaintResponses[keyof DeleteComplaintResponses];

export type GetComplaintData = {
    body?: never;
    path: {
        id: number;
    };
    query?: never;
    url: '/api/complaints/{id}';
};

export type GetComplaintErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
};

export type GetComplaintError = GetComplaintErrors[keyof GetComplaintErrors];

export type GetComplaintResponses = {
    /**
     * Complaint details
     */
    200: {
        data: Complaint;
    };
};

export type GetComplaintResponse = GetComplaintResponses[keyof GetComplaintResponses];

export type UpdateComplaintData = {
    body: {
        complaint?: string;
        resolution?: string;
        resolved_at?: string;
        assignee_id?: number;
    };
    path: {
        id: number;
    };
    query?: never;
    url: '/api/complaints/{id}';
};

export type UpdateComplaintErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
    /**
     * Validation failed
     */
    422: ValidationError;
};

export type UpdateComplaintError = UpdateComplaintErrors[keyof UpdateComplaintErrors];

export type UpdateComplaintResponses = {
    /**
     * Complaint updated successfully
     */
    200: {
        data: Complaint;
    };
};

export type UpdateComplaintResponse = UpdateComplaintResponses[keyof UpdateComplaintResponses];

export type AssignComplaintData = {
    body: {
        assignee_id: number;
    };
    path: {
        id: number;
    };
    query?: never;
    url: '/api/complaints/{id}/assign';
};

export type AssignComplaintErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
    /**
     * Validation failed
     */
    422: ValidationError;
};

export type AssignComplaintError = AssignComplaintErrors[keyof AssignComplaintErrors];

export type AssignComplaintResponses = {
    /**
     * Complaint assigned successfully
     */
    200: {
        data: Complaint;
    };
};

export type AssignComplaintResponse = AssignComplaintResponses[keyof AssignComplaintResponses];

export type ListCustomerStocksData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/customer/stock';
};

export type ListCustomerStocksErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
};

export type ListCustomerStocksError = ListCustomerStocksErrors[keyof ListCustomerStocksErrors];

export type ListCustomerStocksResponses = {
    /**
     * List of stocks
     */
    200: {
        data: Array<Stock>;
        links?: {
            [key: string]: unknown;
        };
        meta?: {
            [key: string]: unknown;
        };
    };
};

export type ListCustomerStocksResponse = ListCustomerStocksResponses[keyof ListCustomerStocksResponses];

export type GetCustomerStockData = {
    body?: never;
    path: {
        stock: number;
    };
    query?: never;
    url: '/api/customer/stock/{stock}';
};

export type GetCustomerStockErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
};

export type GetCustomerStockError = GetCustomerStockErrors[keyof GetCustomerStockErrors];

export type GetCustomerStockResponses = {
    /**
     * Stock details
     */
    200: {
        data: Stock;
    };
};

export type GetCustomerStockResponse = GetCustomerStockResponses[keyof GetCustomerStockResponses];

export type FindStockBySerialData = {
    body?: never;
    path: {
        serial: string;
    };
    query?: never;
    url: '/api/customer/stock/find/{serial}';
};

export type FindStockBySerialErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
};

export type FindStockBySerialError = FindStockBySerialErrors[keyof FindStockBySerialErrors];

export type FindStockBySerialResponses = {
    /**
     * Stock details
     */
    200: {
        data: Stock;
    };
};

export type FindStockBySerialResponse = FindStockBySerialResponses[keyof FindStockBySerialResponses];

export type RedeemWarrantyData = {
    body?: never;
    path: {
        serial: string;
    };
    query?: never;
    url: '/api/customer/stock/redeem/{serial}';
};

export type RedeemWarrantyErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
    /**
     * Validation failed
     */
    422: ValidationError;
};

export type RedeemWarrantyError = RedeemWarrantyErrors[keyof RedeemWarrantyErrors];

export type RedeemWarrantyResponses = {
    /**
     * Warranty redeemed successfully
     */
    200: {
        data: Stock;
    };
};

export type RedeemWarrantyResponse = RedeemWarrantyResponses[keyof RedeemWarrantyResponses];

export type GetUserData = {
    body?: never;
    path: {
        user: number;
    };
    query?: never;
    url: '/api/users/{user}';
};

export type GetUserErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
};

export type GetUserError = GetUserErrors[keyof GetUserErrors];

export type GetUserResponses = {
    /**
     * User details
     */
    200: User;
};

export type GetUserResponse = GetUserResponses[keyof GetUserResponses];

export type UpdateUserNameData = {
    body: {
        name: string;
    };
    path?: never;
    query?: never;
    url: '/api/users/update-name';
};

export type UpdateUserNameErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Validation failed
     */
    422: ValidationError;
};

export type UpdateUserNameError = UpdateUserNameErrors[keyof UpdateUserNameErrors];

export type UpdateUserNameResponses = {
    /**
     * Name updated successfully
     */
    200: {
        message?: string;
        user?: User;
    };
};

export type UpdateUserNameResponse = UpdateUserNameResponses[keyof UpdateUserNameResponses];

export type GetAgreeablesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/agreeables';
};

export type GetAgreeablesErrors = {
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
};

export type GetAgreeablesError = GetAgreeablesErrors[keyof GetAgreeablesErrors];

export type GetAgreeablesResponses = {
    /**
     * Agreeables details
     */
    200: Array<Agreeable>;
};

export type GetAgreeablesResponse = GetAgreeablesResponses[keyof GetAgreeablesResponses];

export type ActivateDealershipData = {
    body: {
        dealership_activation_code: string;
    };
    path?: never;
    query?: never;
    url: '/api/dealer/activate';
};

export type ActivateDealershipErrors = {
    /**
     * Bad request
     */
    400: ValidationError;
    /**
     * Unauthorized
     */
    401: UnauthorizedError;
    /**
     * Forbidden
     */
    403: ForbiddenError;
    /**
     * Resource not found
     */
    404: NotFoundError;
    /**
     * Validation failed
     */
    422: ValidationError;
};

export type ActivateDealershipError = ActivateDealershipErrors[keyof ActivateDealershipErrors];

export type ActivateDealershipResponses = {
    /**
     * Dealership activated successfully
     */
    200: User;
};

export type ActivateDealershipResponse = ActivateDealershipResponses[keyof ActivateDealershipResponses];