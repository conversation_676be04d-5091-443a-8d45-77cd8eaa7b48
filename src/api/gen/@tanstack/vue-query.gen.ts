// This file is auto-generated by @hey-api/openapi-ts

import type { Options } from '@hey-api/client-axios';
import { queryOptions, type UseMutationOptions } from '@tanstack/vue-query';
import type { LoginUserData, LoginUserError, LoginUserResponse, LoginWithOtpData, LoginWithOtpError, LoginWithOtpResponse, SendOtpData, SendOtpError, LogoutUserData, LogoutUserError, ListProductsData, GetProductData, ListComplaintsData, CreateComplaintData, CreateComplaintError, CreateComplaintResponse, DeleteComplaintData, DeleteComplaintError, DeleteComplaintResponse, GetComplaintData, UpdateComplaintData, UpdateComplaintError, UpdateComplaintResponse, AssignComplaintData, AssignComplaintError, AssignComplaintResponse, ListCustomerStocksData, GetCustomerStockData, FindStockBySerialData, RedeemWarrantyData, RedeemWarrantyError, RedeemWarrantyResponse, GetUserData, UpdateUserNameData, UpdateUserNameError, UpdateUserNameResponse, GetAgreeablesData, ActivateDealershipData, ActivateDealershipError, ActivateDealershipResponse } from '../types.gen';
import type { AxiosError } from 'axios';
import { loginUser, loginWithOtp, sendOtp, logoutUser, listProducts, getProduct, listComplaints, createComplaint, deleteComplaint, getComplaint, updateComplaint, assignComplaint, listCustomerStocks, getCustomerStock, findStockBySerial, redeemWarranty, getUser, updateUserName, getAgreeables, activateDealership, client } from '../sdk.gen';

type QueryKey<TOptions extends Options> = [
    Pick<TOptions, 'baseURL' | 'body' | 'headers' | 'path' | 'query'> & {
        _id: string;
        _infinite?: boolean;
    }
];

const createQueryKey = <TOptions extends Options>(id: string, options?: TOptions, infinite?: boolean): QueryKey<TOptions>[0] => {
    const params: QueryKey<TOptions>[0] = { _id: id, baseURL: (options?.client ?? client).getConfig().baseURL } as QueryKey<TOptions>[0];
    if (infinite) {
        params._infinite = infinite;
    }
    if (options?.body) {
        params.body = options.body;
    }
    if (options?.headers) {
        params.headers = options.headers;
    }
    if (options?.path) {
        params.path = options.path;
    }
    if (options?.query) {
        params.query = options.query;
    }
    return params;
};

export const loginUserQueryKey = (options: Options<LoginUserData>) => [
    createQueryKey('loginUser', options)
];

export const loginUserOptions = (options: Options<LoginUserData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await loginUser({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: loginUserQueryKey(options)
    });
};

export const loginUserMutation = (options?: Partial<Options<LoginUserData>>) => {
    const mutationOptions: UseMutationOptions<LoginUserResponse, AxiosError<LoginUserError>, Options<LoginUserData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await loginUser({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const loginWithOtpQueryKey = (options: Options<LoginWithOtpData>) => [
    createQueryKey('loginWithOtp', options)
];

export const loginWithOtpOptions = (options: Options<LoginWithOtpData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await loginWithOtp({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: loginWithOtpQueryKey(options)
    });
};

export const loginWithOtpMutation = (options?: Partial<Options<LoginWithOtpData>>) => {
    const mutationOptions: UseMutationOptions<LoginWithOtpResponse, AxiosError<LoginWithOtpError>, Options<LoginWithOtpData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await loginWithOtp({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const sendOtpQueryKey = (options: Options<SendOtpData>) => [
    createQueryKey('sendOtp', options)
];

export const sendOtpOptions = (options: Options<SendOtpData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await sendOtp({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: sendOtpQueryKey(options)
    });
};

export const sendOtpMutation = (options?: Partial<Options<SendOtpData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<SendOtpError>, Options<SendOtpData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await sendOtp({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const logoutUserMutation = (options?: Partial<Options<LogoutUserData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<LogoutUserError>, Options<LogoutUserData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await logoutUser({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const listProductsQueryKey = (options?: Options<ListProductsData>) => [
    createQueryKey('listProducts', options)
];

export const listProductsOptions = (options?: Options<ListProductsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await listProducts({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: listProductsQueryKey(options)
    });
};

export const getProductQueryKey = (options: Options<GetProductData>) => [
    createQueryKey('getProduct', options)
];

export const getProductOptions = (options: Options<GetProductData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getProduct({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getProductQueryKey(options)
    });
};

export const listComplaintsQueryKey = (options?: Options<ListComplaintsData>) => [
    createQueryKey('listComplaints', options)
];

export const listComplaintsOptions = (options?: Options<ListComplaintsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await listComplaints({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: listComplaintsQueryKey(options)
    });
};

export const createComplaintQueryKey = (options: Options<CreateComplaintData>) => [
    createQueryKey('createComplaint', options)
];

export const createComplaintOptions = (options: Options<CreateComplaintData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createComplaint({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createComplaintQueryKey(options)
    });
};

export const createComplaintMutation = (options?: Partial<Options<CreateComplaintData>>) => {
    const mutationOptions: UseMutationOptions<CreateComplaintResponse, AxiosError<CreateComplaintError>, Options<CreateComplaintData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createComplaint({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const deleteComplaintMutation = (options?: Partial<Options<DeleteComplaintData>>) => {
    const mutationOptions: UseMutationOptions<DeleteComplaintResponse, AxiosError<DeleteComplaintError>, Options<DeleteComplaintData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteComplaint({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getComplaintQueryKey = (options: Options<GetComplaintData>) => [
    createQueryKey('getComplaint', options)
];

export const getComplaintOptions = (options: Options<GetComplaintData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getComplaint({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getComplaintQueryKey(options)
    });
};

export const updateComplaintMutation = (options?: Partial<Options<UpdateComplaintData>>) => {
    const mutationOptions: UseMutationOptions<UpdateComplaintResponse, AxiosError<UpdateComplaintError>, Options<UpdateComplaintData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateComplaint({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const assignComplaintQueryKey = (options: Options<AssignComplaintData>) => [
    createQueryKey('assignComplaint', options)
];

export const assignComplaintOptions = (options: Options<AssignComplaintData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await assignComplaint({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: assignComplaintQueryKey(options)
    });
};

export const assignComplaintMutation = (options?: Partial<Options<AssignComplaintData>>) => {
    const mutationOptions: UseMutationOptions<AssignComplaintResponse, AxiosError<AssignComplaintError>, Options<AssignComplaintData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await assignComplaint({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const listCustomerStocksQueryKey = (options?: Options<ListCustomerStocksData>) => [
    createQueryKey('listCustomerStocks', options)
];

export const listCustomerStocksOptions = (options?: Options<ListCustomerStocksData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await listCustomerStocks({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: listCustomerStocksQueryKey(options)
    });
};

export const getCustomerStockQueryKey = (options: Options<GetCustomerStockData>) => [
    createQueryKey('getCustomerStock', options)
];

export const getCustomerStockOptions = (options: Options<GetCustomerStockData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCustomerStock({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCustomerStockQueryKey(options)
    });
};

export const findStockBySerialQueryKey = (options: Options<FindStockBySerialData>) => [
    createQueryKey('findStockBySerial', options)
];

export const findStockBySerialOptions = (options: Options<FindStockBySerialData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await findStockBySerial({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: findStockBySerialQueryKey(options)
    });
};

export const redeemWarrantyQueryKey = (options: Options<RedeemWarrantyData>) => [
    createQueryKey('redeemWarranty', options)
];

export const redeemWarrantyOptions = (options: Options<RedeemWarrantyData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await redeemWarranty({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: redeemWarrantyQueryKey(options)
    });
};

export const redeemWarrantyMutation = (options?: Partial<Options<RedeemWarrantyData>>) => {
    const mutationOptions: UseMutationOptions<RedeemWarrantyResponse, AxiosError<RedeemWarrantyError>, Options<RedeemWarrantyData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await redeemWarranty({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getUserQueryKey = (options: Options<GetUserData>) => [
    createQueryKey('getUser', options)
];

export const getUserOptions = (options: Options<GetUserData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getUser({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getUserQueryKey(options)
    });
};

export const updateUserNameMutation = (options?: Partial<Options<UpdateUserNameData>>) => {
    const mutationOptions: UseMutationOptions<UpdateUserNameResponse, AxiosError<UpdateUserNameError>, Options<UpdateUserNameData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateUserName({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getAgreeablesQueryKey = (options?: Options<GetAgreeablesData>) => [
    createQueryKey('getAgreeables', options)
];

export const getAgreeablesOptions = (options?: Options<GetAgreeablesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAgreeables({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAgreeablesQueryKey(options)
    });
};

export const activateDealershipQueryKey = (options: Options<ActivateDealershipData>) => [
    createQueryKey('activateDealership', options)
];

export const activateDealershipOptions = (options: Options<ActivateDealershipData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await activateDealership({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: activateDealershipQueryKey(options)
    });
};

export const activateDealershipMutation = (options?: Partial<Options<ActivateDealershipData>>) => {
    const mutationOptions: UseMutationOptions<ActivateDealershipResponse, AxiosError<ActivateDealershipError>, Options<ActivateDealershipData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await activateDealership({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};