// This file is auto-generated by @hey-api/openapi-ts

import { createClient, createConfig, type Options } from '@hey-api/client-axios';
import type { LoginUserData, LoginUserResponse, LoginUserError, LoginWithOtpData, LoginWithOtpResponse, LoginWithOtpError, SendOtpData, SendOtpError, LogoutUserData, LogoutUserError, ListProductsData, ListProductsResponse, GetProductData, GetProductResponse, GetProductError, ListComplaintsData, ListComplaintsResponse, ListComplaintsError, CreateComplaintData, CreateComplaintResponse, CreateComplaintError, DeleteComplaintData, DeleteComplaintResponse, DeleteComplaintError, GetComplaintData, GetComplaintResponse, GetComplaintError, UpdateComplaintData, UpdateComplaintResponse, UpdateComplaintError, AssignComplaintData, AssignComplaintResponse, AssignComplaintError, ListCustomerStocksData, ListCustomerStocksResponse, ListCustomerStocksError, GetCustomerStockData, GetCustomerStockResponse, GetCustomerStockError, FindStockBySerialData, FindStockBySerialResponse, FindStockBySerialError, RedeemWarrantyData, RedeemWarrantyResponse, RedeemWarrantyError, GetUserData, GetUserResponse, GetUserError, UpdateUserNameData, UpdateUserNameResponse, UpdateUserNameError, GetAgreeablesData, GetAgreeablesResponse, GetAgreeablesError, ActivateDealershipData, ActivateDealershipResponse, ActivateDealershipError } from './types.gen';

export const client = createClient(createConfig({
    throwOnError: true
}));

/**
 * Login for staff and dealers
 */
export const loginUser = <ThrowOnError extends boolean = true>(options: Options<LoginUserData, ThrowOnError>) => {
    return (options?.client ?? client).post<LoginUserResponse, LoginUserError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/auth/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Login with OTP for customers
 */
export const loginWithOtp = <ThrowOnError extends boolean = true>(options: Options<LoginWithOtpData, ThrowOnError>) => {
    return (options?.client ?? client).post<LoginWithOtpResponse, LoginWithOtpError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/auth/login-otp',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Send OTP for customer login
 */
export const sendOtp = <ThrowOnError extends boolean = true>(options: Options<SendOtpData, ThrowOnError>) => {
    return (options?.client ?? client).post<unknown, SendOtpError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/auth/send-otp',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Logout user
 */
export const logoutUser = <ThrowOnError extends boolean = true>(options?: Options<LogoutUserData, ThrowOnError>) => {
    return (options?.client ?? client).delete<unknown, LogoutUserError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/auth/logout',
        ...options
    });
};

/**
 * List all products
 */
export const listProducts = <ThrowOnError extends boolean = true>(options?: Options<ListProductsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ListProductsResponse, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/products',
        ...options
    });
};

/**
 * Get product details
 */
export const getProduct = <ThrowOnError extends boolean = true>(options: Options<GetProductData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetProductResponse, GetProductError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/products/{id}',
        ...options
    });
};

/**
 * List complaints based on user role
 */
export const listComplaints = <ThrowOnError extends boolean = true>(options?: Options<ListComplaintsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ListComplaintsResponse, ListComplaintsError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/complaints',
        ...options
    });
};

/**
 * Create a new complaint
 */
export const createComplaint = <ThrowOnError extends boolean = true>(options: Options<CreateComplaintData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateComplaintResponse, CreateComplaintError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/complaints',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a complaint
 */
export const deleteComplaint = <ThrowOnError extends boolean = true>(options: Options<DeleteComplaintData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteComplaintResponse, DeleteComplaintError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/complaints/{id}',
        ...options
    });
};

/**
 * Get complaint details
 */
export const getComplaint = <ThrowOnError extends boolean = true>(options: Options<GetComplaintData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetComplaintResponse, GetComplaintError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/complaints/{id}',
        ...options
    });
};

/**
 * Update a complaint
 */
export const updateComplaint = <ThrowOnError extends boolean = true>(options: Options<UpdateComplaintData, ThrowOnError>) => {
    return (options?.client ?? client).put<UpdateComplaintResponse, UpdateComplaintError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/complaints/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Assign a complaint to a staff member
 */
export const assignComplaint = <ThrowOnError extends boolean = true>(options: Options<AssignComplaintData, ThrowOnError>) => {
    return (options?.client ?? client).post<AssignComplaintResponse, AssignComplaintError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/complaints/{id}/assign',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * List customer's stocks
 */
export const listCustomerStocks = <ThrowOnError extends boolean = true>(options?: Options<ListCustomerStocksData, ThrowOnError>) => {
    return (options?.client ?? client).get<ListCustomerStocksResponse, ListCustomerStocksError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/customer/stock',
        ...options
    });
};

/**
 * Get stock details
 */
export const getCustomerStock = <ThrowOnError extends boolean = true>(options: Options<GetCustomerStockData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetCustomerStockResponse, GetCustomerStockError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/customer/stock/{stock}',
        ...options
    });
};

/**
 * Find stock by serial number
 */
export const findStockBySerial = <ThrowOnError extends boolean = true>(options: Options<FindStockBySerialData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindStockBySerialResponse, FindStockBySerialError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/customer/stock/find/{serial}',
        ...options
    });
};

/**
 * Redeem warranty for a stock
 */
export const redeemWarranty = <ThrowOnError extends boolean = true>(options: Options<RedeemWarrantyData, ThrowOnError>) => {
    return (options?.client ?? client).post<RedeemWarrantyResponse, RedeemWarrantyError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/customer/stock/redeem/{serial}',
        ...options
    });
};

/**
 * Get user details
 */
export const getUser = <ThrowOnError extends boolean = true>(options: Options<GetUserData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetUserResponse, GetUserError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/{user}',
        ...options
    });
};

/**
 * Update authenticated user's name
 */
export const updateUserName = <ThrowOnError extends boolean = true>(options: Options<UpdateUserNameData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateUserNameResponse, UpdateUserNameError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/update-name',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Get agreeables
 */
export const getAgreeables = <ThrowOnError extends boolean = true>(options?: Options<GetAgreeablesData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetAgreeablesResponse, GetAgreeablesError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/agreeables',
        ...options
    });
};

/**
 * Activate dealership
 */
export const activateDealership = <ThrowOnError extends boolean = true>(options: Options<ActivateDealershipData, ThrowOnError>) => {
    return (options?.client ?? client).post<ActivateDealershipResponse, ActivateDealershipError, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/dealer/activate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};