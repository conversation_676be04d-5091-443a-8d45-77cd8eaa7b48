import type { AccessTokenEvent } from '@/types';
import { AxiosError } from 'axios';
import { useEventBus } from '@vueuse/core';
import { client } from './gen';

client.setConfig({
	baseURL: import.meta.env.VITE_API_URL,
	throwOnError: true,
});

const { on: onAccessTokenEvent, emit: emitAccessTokenEvent } = useEventBus<AccessTokenEvent>('access-token');

let accessToken: string | undefined = undefined;

onAccessTokenEvent((e) => {
	if (e.type === 'access-token-set') {
		accessToken = e.token;
	} else if (e.type === 'access-token-unset') {
		accessToken = undefined;
	}
});

// interceptors

client.instance.interceptors.request.use((config) => {
	if (!config.headers.Authorization && accessToken) {
		config.headers.Authorization = `Bearer ${accessToken}`;
	}
	return config;
});

client.instance.interceptors.response.use(undefined, (err) => {
	if (err instanceof AxiosError && err.response?.status === 401 && accessToken) {
		emitAccessTokenEvent({
			type: 'access-token-unset-request',
		});
	}
	throw err;
});

export const errToMessage = (err: any, fallback?: string) => {
	if (err instanceof AxiosError && err.response?.data) {
		const msg =
			err.response.data.message || Object.values(err.response.data).join(' ') || fallback || 'An error occurred!';
		// 404 request will have a message like 'No query results for model [App\Models\Stock].' We want to change it to 'Stock not found'.
		// The model name may be anything, so we need to find and replace it.
		if (typeof msg === 'string' && err.response.status === 404 && msg.match(/No query results for model/)) {
			const modelName = msg.match(/No query results for model \[(.*)\]/)?.[1];
			return `No ${modelName?.split('\\').pop()?.toLowerCase() ?? 'result'} found.`;
		}
        return msg;
	}
    if(typeof err === 'string') {
        return err;
    }
    if(err instanceof Error && err.message) {
        return err.message || fallback || 'An error occurred!';
    }

	return fallback || 'An error occurred!';
};

export * from './gen';
export * from './gen/@tanstack/vue-query.gen';
