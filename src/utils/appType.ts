import FamilyLogoText from '@/assets/family/logo-text.png';
import PartnerLogoText from '@/assets/partner/logo-text.png';
import FamilyLogoImage from '@/assets/family/logo-image.png';
import PartnerLogoImage from '@/assets/partner/logo-image.png';
import GenericLogoText from '@/assets/shared/logo-text.png';
import GenericLogoImage from '@/assets/shared/logo-image.png';

export const appType = import.meta.env.VITE_APP_TYPE || 'family' as 'family' | 'partner';

export const appTypeSelect = <T>(items: { family: T; partner: T; default?: T }): T => {
	switch (appType) {
		case 'family':
			return items.family;
		case 'partner':
			return items.partner;
		default:
			return items.default ?? items.family;
	}
};

export const isFamily = appType === 'family';
export const isPartner = appType === 'partner';

export const branding = {
	logoText: appTypeSelect({
		partner: {
			src: PartnerLogoText,
			alt: 'Ralicoan Partner Logo',
		},
		family: {
			src: FamilyLogoText,
			alt: 'Ralicoan Family Logo',
		},
	}),
	logoImage: appTypeSelect({
		partner: {
			src: PartnerLogoImage,
			alt: 'Ralicoan Partner Logo',
		},
		family: {
			src: FamilyLogoImage,
			alt: 'Ralicoan Family Logo',
		},
	}),
	logoTextGeneric: {
		src: GenericLogoText,
		alt: 'Ralicoan Batteries Logo',
	},
	logoImageGeneric: {
		src: GenericLogoImage,
		alt: 'Ralicoan Batteries Logo',
	},
};
