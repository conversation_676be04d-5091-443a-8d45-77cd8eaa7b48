export * from './appType';

import type { FormActions } from 'vee-validate';
import { AxiosError } from 'axios';
import { z } from 'zod';

const ValidationErrors = z.object({
	errors: z.record(z.string(), z.string().array()),
});

/**
 * Set validation errors on a form instance from an axios error.
 *
 * Only works for normalized errors of ValidationErrors and a 422 response status.
 * @returns `true` if error was handled. `false`, otherwise.
 */
export const setValidationErrors = <T extends Record<string, unknown>>(
	actions: FormActions<T>,
	e: unknown,
	log = true
) => {
	if (e instanceof AxiosError && e.response?.status === 422) {
		const validationErrors = ValidationErrors.safeParse(e.response.data);
		console.log(e.response.data, validationErrors);
		if (validationErrors.success) {
			//@ts-expect-error Could pass an array of errors to set in the method in the future.
			actions.setErrors(validationErrors.data.errors);
			for (const key of Object.keys(validationErrors.data.errors)) {
				if (Object.prototype.hasOwnProperty.call(validationErrors.data.errors, key)) {
					actions.setFieldTouched(key as any, true);
				}
			}
			return true;
		}
	}
	if (log) {
		if (Object.prototype.hasOwnProperty.call(e, 'message')) {
			// @ts-expect-error hasOwnProperty narrows
			console.log(`Not a validation error. Message: '${e.message}'.`);
		} else {
			console.log('Not a validation error. Error:\n', e);
		}
	}
	return false;
};
