<script setup lang="ts">
const props = defineProps<{
	error?: string | null;
	disabled?: boolean;
	placeholder?: string;
	help?: string | null;
	label?: string;
	options?: Array<{ value: string | number; label: string }>;
}>();

const model = defineModel<string | number | null | undefined>({ required: true });
</script>

<template>
	<div class="w-full flex flex-col select-field">
		<div v-show="!!label" class="flex justify-between items-center">
			<label for="">{{ label }}</label>
			<slot name="icon"> </slot>
		</div>

		<select
			v-model="model"
			class="select-bb"
			:disabled="disabled"
		>
			<option v-if="placeholder" value="" disabled>{{ placeholder }}</option>
			
			<!-- Options from props -->
			<option v-for="option in options" :key="option.value" :value="option.value">
				{{ option.label }}
			</option>
			
			<!-- Options from slot -->
			<slot></slot>
		</select>

		<div v-show="!!error" class="mt-1">
			<p class="text-red-500">{{ error }}</p>
		</div>

		<div v-show="!!help" class="mt-1">
			<p class="text-gray-500">{{ help }}</p>
		</div>
	</div>
</template>
