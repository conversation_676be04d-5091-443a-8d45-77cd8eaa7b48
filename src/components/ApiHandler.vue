<script
	setup
	lang="ts"
	generic="TQueryFnData = unknown, TError = Error, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey"
>
import { errToMessage } from '@/api';
import { useQuery, type QueryKey } from '@tanstack/vue-query';
import Loader from './Loader.vue';

defineProps<{
	query: Partial<ReturnType<typeof useQuery<TQueryFnData, TError, TData, TQueryKey>>>;
	errorFallback?: string;
	bgLoader?: boolean;
}>();
</script>

<template>
	<div v-if="query.isLoading?.value" class="flex justify-center w-full">
		<slot name="loading"> <Loader /> </slot>
	</div>
	<div v-else-if="query.error?.value" class="self-center text-red-500">
		{{ errToMessage(query.error?.value, errorFallback) }}
	</div>
	<template v-else-if="query.data?.value != null">
		<slot name="default" :data="query.data?.value"></slot>
		<!-- Background Loader -->
		<Loader
			v-if="bgLoader && query.isFetching?.value"
			class="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 opacity-30"
		/>
	</template>
	<slot v-else name="empty"> </slot>
</template>
