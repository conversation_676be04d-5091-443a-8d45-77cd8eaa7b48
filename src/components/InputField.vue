<script setup lang="ts">
import { uniqueId } from 'lodash';
import { computed } from 'vue';

const props = defineProps<{
	error?: string | null;
	disabled?: boolean;
	placeholder?: string;
	help?: string | null;
	label?: string;
	textarea?: boolean;
	id?: string;
}>();

const fallbackId = uniqueId('input-');
const id = computed(() => props.id || fallbackId);

const model = defineModel<string | number | null | undefined>({ required: true });
</script>

<template>
	<div class="w-full flex flex-col input-field">
		<div v-show="!!label" class="flex justify-between items-center">
			<label :for="id">{{ label }}</label>
			<slot name="icon"> </slot>
		</div>

		<input
			v-if="!textarea"
			v-model="model"
			type="text"
			class="input-bb"
			:placeholder="placeholder"
			:disabled="disabled"
			:id="id"
		/>
		<textarea v-else v-model="model" class="input-bb" :placeholder="placeholder" :disabled="disabled"></textarea>

		<div v-show="!!error" class="mt-1">
			<p class="text-red-500">{{ error }}</p>
		</div>

		<div v-show="!!help" class="mt-1" aria-roledescription="">
			<p class="text-gray-500">{{ help }}</p>
		</div>
	</div>
</template>
