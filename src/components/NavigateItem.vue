<script setup lang="ts">
defineProps<{
	title: string;
	subtitle: string;
}>();
</script>

<template>
	<div class="navigate-item mb-5">
		<slot>
			<h2 class="heading font-semibold">
				{{ title }}
			</h2>
			<p class="font-semibold">
				{{ subtitle }}
			</p>
		</slot>
	</div>
</template>

<style scoped>
.navigate-item {
	@apply bg-[#f0f0f0] text-[#0A1C7D] border-2 border-[#0A1C7D] px-5 w-full h-14 rounded-lg flex justify-between items-center text-lg font-semibold cursor-pointer;
}
.navigate-item.disabled {
	@apply text-[hsl(231,25%,50%)]  opacity-65 cursor-not-allowed;
}
</style>
