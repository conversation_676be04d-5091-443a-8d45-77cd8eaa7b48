<script setup lang="ts">
import { toMainMenu } from '~/customer';
import { useRouter } from 'vue-router';
import { branding } from '@/utils';
import { QueryClient } from '@tanstack/vue-query';
import { getAgreeablesOptions } from '@/api';

const router = useRouter();

const queryClient = new QueryClient();
queryClient.prefetchQuery({
	...getAgreeablesOptions(),
	staleTime: 60 * 1000,
});

setTimeout(async () => {
	if (
		typeof router.currentRoute.value?.query?.redirectTo === 'string' &&
		router.resolve(router.currentRoute.value.query.redirectTo).matched.length > 0
	) {
		const navError = await router.push(router.currentRoute.value.query.redirectTo);
		if (navError != null) {
			console.log('Could not navigate to ', router.currentRoute.value.query.redirectTo, navError);
			toMainMenu(router);
		}
	} else {
		toMainMenu(router);
	}
}, 3000);
</script>
<template>
	<div>
		<div class="h-screen flex flex-col justify-center">
			<div class="flex justify-center items-center w-full">
				<img v-bind="branding.logoImage" class="w-44" />
			</div>

			<div class="mx-10 my-20 flex justify-center items-center flex-col gap-5">
				<h1 class="text-4xl text-[#00703C]">Welcome to</h1>
				<img v-bind="branding.logoText" />
			</div>
		</div>
	</div>
</template>
