services:
    laravel.test:
        build:
            context: ./docker/8.4
            dockerfile: Dockerfile
            args:
                WWWGROUP: "${WWWGROUP}"
        image: sail-8.4/app
        extra_hosts:
            - "host.docker.internal:host-gateway"
        ports:
            - "${APP_PORT:-80}:80"
        environment:
            WWWUSER: "${WWWUSER}"
            LARAVEL_SAIL: 1
            XDEBUG_MODE: "${SAIL_XDEBUG_MODE:-off}"
            XDEBUG_CONFIG: "${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}"
            IGNITION_LOCAL_SITES_PATH: "${PWD}"
        volumes:
            - ".:/var/www/html"
        networks:
            - sail
        depends_on:
            - pgsql
            - redis
            - mailpit
    pgsql:
        image: "postgres:17"
        ports:
            - "${FORWARD_DB_PORT:-5432}:5432"
        environment:
            PGPASSWORD: "${DB_PASSWORD:-secret}"
            POSTGRES_DB: "${DB_DATABASE}"
            POSTGRES_USER: "${DB_USERNAME}"
            POSTGRES_PASSWORD: "${DB_PASSWORD:-secret}"
        volumes:
            - "sail-pgsql:/var/lib/postgresql/data"
            - "./docker/pgsql/create-testing-database.sql:/docker-entrypoint-initdb.d/10-create-testing-database.sql"
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - pg_isready
                - "-q"
                - "-d"
                - "${DB_DATABASE}"
                - "-U"
                - "${DB_USERNAME}"
            retries: 3
            timeout: 5s
    redis:
        image: "redis:alpine"
        ports:
            - "${FORWARD_REDIS_PORT:-6379}:6379"
        volumes:
            - "sail-redis:/data"
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - redis-cli
                - ping
            retries: 3
            timeout: 5s
    redisinsight:
        image: "redis/redisinsight"
        ports:
            - "5540:5540"
        environment:
            - RI_REDIS_HOST=redis
            - RI_REDIS_PORT=6379
        networks:
            - sail
    zookeeper:
        image: "confluentinc/cp-zookeeper:7.4.4"
        environment:
            ZOOKEEPER_CLIENT_PORT: 2181
            ZOOKEEPER_TICK_TIME: 2000
        ports:
            - "2181:2181"
        networks:
            - sail
    kafka:
        image: "confluentinc/cp-kafka:7.4.4"
        depends_on:
            - zookeeper
        ports:
            - "9092:9092"
        networks:
            - sail
        environment:
            KAFKA_BROKER_ID: 1
            KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181"
            KAFKA_ADVERTISED_LISTENERS: "PLAINTEXT://kafka:9092"
            KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: "PLAINTEXT:PLAINTEXT"
            KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
            KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    kafka-ui:
        container_name: kafka-ui
        image: "provectuslabs/kafka-ui:latest"
        ports:
            - "8089:8080"
        networks:
            - sail
        environment:
            DYNAMIC_CONFIG_ENABLED: true
        volumes:
            - "kafka-config:/etc/kafkaui"
    mailpit:
        image: "axllent/mailpit:latest"
        ports:
            - "${FORWARD_MAILPIT_PORT:-1025}:1025"
            - "${FORWARD_MAILPIT_DASHBOARD_PORT:-8025}:8025"
        networks:
            - sail
networks:
    sail:
        driver: bridge
volumes:
    sail-pgsql:
        driver: local
    sail-redis:
        driver: local
    sail-mysql:
        driver: local
    kafka-config:
        driver: local
