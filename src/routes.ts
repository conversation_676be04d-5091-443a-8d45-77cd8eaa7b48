import LandPage from './components/LandPage.vue';
import { createRouter, createWebHistory, type RouteRecordRaw, type Router } from 'vue-router';
import loginRoutes from '~/login/routes';
import warrantyRoutes from '~/customer/routes';
import complaintRoutes from '~/complaint/routes';

const routes: RouteRecordRaw[] = [
	{
		name: 'land-page',
		component: LandPage,
		path: '/',
		meta: { page: -1, transition: 'fade' },
	},
	...loginRoutes(),
	...warrantyRoutes(),
	...complaintRoutes(),
];

const router = createRouter({
	history: createWebHistory(),
	routes,
});

let shownLandingPage = false;
router.beforeEach((to, from) => {
	if (shownLandingPage) {
		return;
	}

	shownLandingPage = true;
	if (to.name !== 'land-page') {
		return {
			name: 'land-page',
			query: { redirectTo: to.fullPath },
		};
	}
});

export default router;

export const toLandPage = (router: Router, redirectTo?: string) =>
	router.push({ name: 'land-page', query: { redirectTo } });
