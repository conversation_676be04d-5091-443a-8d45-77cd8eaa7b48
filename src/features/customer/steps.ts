export enum Steps {
  RegisterWarrantyOnline = 0,
  ShowRegisterWarranty = 1,
  RegisteredSuccessfully = 2,
}

export enum checkWarrantyStatus {
  WarrantyCheckStatusBack = 0,
  WarrantyCheckStatusNext = 1,
}

export enum complaintRegister {
  RegisterComplaintBack = 0,
  RegisterComplaintNext = 1,
  RegisterComplaint = 2,
  CheckComplaintStatusBack = 3,
  CheckcomplaintStatusNext = 4,
}

export enum complaintCheck {
  CheckComplaintStatusBack = 0,
  CheckcomplaintStatusNext = 1,
}

export enum buyWarranty {
  BuyExtendedWarrantyBack = 0,
  BuyExtendedWarrantyNext = 1,
  BuyExtendedWarrantySuccessful = 2,
}


