import type { Router, RouteRecordRaw } from 'vue-router';
import MainMenu from './components/MainMenu.vue';
import RegisterWarrantyPage from './components/RegisterWarrantyPage.vue';
import WarrantyCheckPage from './components/WarrantyCheckPage.vue';
import BuyExtendedWarrantyPage from './components/BuyExtendedWarrantyPage.vue';
import WarrantyPage from './components/WarrantyPage.vue';

export default function (): RouteRecordRaw[] {
	return [
		{
			name: 'menu',
			component: MainMenu,
			path: '/menu',
			meta: { page: 0 },
		},
		{
			name: 'warranty',
			component: RegisterWarrantyPage,
			path: '/warranty',
		},
		{
			name: 'warranty-check',
			component: WarrantyCheckPage,
			path: '/warranty-check',
		},
		{
			name: 'buy-extended-warranty',
			component: BuyExtendedWarrantyPage,
			path: '/buy-extended-warranty',
		},
		{
			name: 'view-warranty',
			component: WarrantyPage,
			path: '/view-warranty/:warrantyId',
		},
	];
}

export const toMainMenu = (router: Router) => router.push({ name: 'menu' });
export const toWarranty = (router: Router) => router.push({ name: 'warranty' });
export const toWarrantyCheck = (router: Router) => router.push({ name: 'warranty-check' });
export const toBuyExtendedWarranty = (router: Router) => router.push({ name: 'buy-extended-warranty' });
export const toWarrantyView = (router: Router, id: number, success?: boolean) =>
	router.push({
		name: 'view-warranty',
		params: { warrantyId: id },
		query: { success: success ? 'true' : undefined },
	});
