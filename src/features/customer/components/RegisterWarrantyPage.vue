<script lang="ts" setup>
import { computed, ref } from 'vue';
import DefaultLayout from '@/components/DefaultLayout.vue';
import { toMainMenu, toWarrantyView } from '..';
import { useRouter } from 'vue-router';
import { errToMessage, findStockBySerial, redeemWarrantyMutation } from '@/api';
import { storeToRefs } from 'pinia';
import { useAuthStore } from '~/login';
import ApiHandler from '@/components/ApiHandler.vue';
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query';
import InputField from '@/components/InputField.vue';

const router = useRouter();
const { user } = storeToRefs(useAuthStore());

const serialNumber = ref('');

const stockQueryKey = computed(() => ['stock', serialNumber.value]);
const stockQuery = useQuery({
	queryKey: stockQueryKey,
	queryFn: async () => {
		if (!serialNumber.value) {
			await new Promise((resolve) => setTimeout(resolve, 100));
			throw 'Please enter a serial number';
		}

		const r = await findStockBySerial({
			path: {
				serial: serialNumber.value,
			},
		});
		return r.data;
	},
	enabled: false,
});
const resetStock = () => {
	if (!stockQuery.isFetched.value || stockQuery.isError.value) {
		return;
	}

	queryClient.removeQueries({
		queryKey: stockQueryKey.value,
	});
	serialNumber.value = '';
	redeemWarranty.reset();
};

const canRegister = computed(
	() =>
		stockQuery.data.value != null &&
		stockQuery.data.value.data.warrantee_id == null &&
		stockQuery.data.value.data.serial_number != null
);

const queryClient = useQueryClient();
const redeemWarranty = useMutation({
	...redeemWarrantyMutation(),
	onMutate: async (req) => {
		if (!req.path?.serial) throw 'Please enter a serial number';
		return req;
	},
	onSuccess(data) {
		queryClient.invalidateQueries({
			queryKey: stockQueryKey.value,
		});
		toWarrantyView(router, data.data.id, true);
	},
});
</script>

<template>
	<DefaultLayout>
		<div class="flex justify-between flex-col p-10 grow">
			<div class="">
				<h1 class="page-title">Register Warranty Online</h1>

				<InputField
					v-model="serialNumber"
					label="Serial No."
					help="Enter your unique serial number."
					:disabled="!!stockQuery.data.value || redeemWarranty.isPending.value"
					@input="resetStock"
					@click="resetStock"
				>
					<template #icon>
						<i v-show="!stockQuery.data.value" class="bx bx-qr-scan text-xl mr-5"></i>
						<i
							v-show="!!stockQuery.data.value"
							class="bx bx-x text-xl text-red-500 mr-5"
							@click="resetStock"
						></i>
					</template>
				</InputField>
			</div>

			<ApiHandler :query="stockQuery" v-slot="{ data }">
				<div class="w-full">
					<div v-for="(value, key) of data.data.product?.specifications" class="flex flex-row w-full mb-5">
						<h1 class="heading mr-3">{{ key }}:</h1>
						<p class="font-semibold">{{ value }}</p>
					</div>
					<div class="flex flex-row w-full mb-5">
						<h1 class="heading mr-3">Model Name:</h1>
						<p class="font-semibold">{{ data.data.product?.name }}</p>
					</div>
					<div class="flex flex-row w-full mb-5">
						<h1 class="heading mr-3">Warranty:</h1>
						<p class="font-semibold">
							{{ (data.data.warranty?.description ?? data.data.product?.warranty?.description) || 'No Warranty' }}
						</p>
					</div>
				</div>

				<div
					v-if="data.data.warrantee_id === user?.id"
					class="flex justify-center items-center flex-col mt-16 gap-1"
				>
					<p class="text-[#00703C] text-center">
						The Warranty of this battery is already registered with you.
					</p>
				</div>
				<div
					v-if="data.data.warrantee_id !== user?.id && data.data.warrantee_id != null"
					class="flex justify-center items-center flex-col mt-16 gap-1"
				>
					<p class="text-red-500 text-center">
						The Warranty of this battery is already registered with someone else.
					</p>
				</div>
			</ApiHandler>

			<div class="flex flex-col items-stretch mt-auto gap-3">
				<p v-if="redeemWarranty.error.value" class="self-center text-red-500">
					{{ errToMessage(redeemWarranty.error.value) }}
				</p>
				<div class="flex justify-between gap-5">
					<button
						class="btn-secondary"
						@click="toMainMenu(router)"
						:disabled="stockQuery.isLoading.value || redeemWarranty.isPending.value"
					>
						Cancel
					</button>
					<button
						v-if="!stockQuery.data.value"
						class="btn-primary"
						@click="stockQuery.refetch()"
						:disabled="stockQuery.isLoading.value"
					>
						Find Warranty
					</button>
					<button
						v-else
						class="btn-primary"
						:disabled="redeemWarranty.isPending.value"
						@click="
							canRegister
								? redeemWarranty.mutate({
										path: {
											serial: stockQuery.data.value.data.serial_number,
										},
									})
								: resetStock()
						"
					>
						{{	
							redeemWarranty.isPending.value
								? 'Registering...'
								: canRegister
									? 'Register Now!'
									: 'Try Another'
						}}
					</button>
				</div>
			</div>
		</div>
	</DefaultLayout>
</template>
