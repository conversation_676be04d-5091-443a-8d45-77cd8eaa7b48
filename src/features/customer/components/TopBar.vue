<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { toMainMenu } from '..';
import { branding } from '@/utils';

const route = useRoute();
const router = useRouter();
</script>

<template>
	<div class="top-bar flex flow-row items-center bg-[#0a1c7d] w-full h-auto pl-5">
		<!-- <div v-if="route.name === 'menu'" class="text-white text-4xl cursor-pointer" @click="logOut">
			<i class="bx bx-log-out"></i>
		</div> -->
		<div v-if="route.name !== 'menu'" class="text-white text-4xl cursor-pointer" @click="toMainMenu(router)">
			<i class="bx bx-arrow-back"></i>
		</div>
		<div class="w-full flex justify-center items-center mr-8">
			<img v-bind="branding.logoTextGeneric" class="w-44 text-center" />
		</div>
	</div>
</template>

<style scoped>
.top-bar {
	padding-top: max(20px, calc( env(safe-area-inset-top, 0px) + 10px ) );
	min-height: 80px;
}
</style>
