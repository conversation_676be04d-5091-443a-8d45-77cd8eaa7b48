<script setup lang="ts">
import type { Stock } from '@/api';

defineProps<{
	warranty: Stock | undefined | null;
}>();
</script>

<template>
	<div v-show="warranty" class="w-full">
		<div class="flex flex-col w-full mb-5">
			<h1 class="heading mr-3">Model Name:</h1>
			<p class="font-semibold">{{ warranty?.product?.name }}</p>
		</div>
		<div class="flex flex-col w-full mb-5">
			<h1 class="heading mr-3">Warranty Start Date:</h1>
			<p class="font-semibold">
				{{ warranty?.warranty_start ? new Date(warranty?.warranty_start).toLocaleDateString() : '-' }}
			</p>
		</div>
		<div class="flex flex-col w-full mb-5">
			<h1 class="heading mr-3">Warranty Expiry Date:</h1>
			<p class="font-semibold">
				{{ warranty?.warranty_end ? new Date(warranty?.warranty_end).toLocaleDateString() : '-' }}
			</p>
		</div>
		<div v-for="(value, key) of warranty?.product?.specifications" class="flex flex-col w-full mb-5">
			<h1 class="heading mr-3">{{ key }}:</h1>
			<p class="font-semibold">{{ value }}</p>
		</div>
	</div>
</template>
