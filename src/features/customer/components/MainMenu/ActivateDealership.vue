<script setup lang="ts">
import { activateDealershipMutation } from '@/api';
import { useAuthStore } from '~/login';
import { useMutation } from '@tanstack/vue-query';
import { ref } from 'vue';
import { errToMessage } from '@/api';
import Loader from '@/components/Loader.vue';

const authStore = useAuthStore();

const activationCode = ref('');

const activate = useMutation({
	...activateDealershipMutation(),
	onSuccess: async () => {
		await authStore.refreshUser();
	},
});
</script>

<template>
	<div class="flex  flex-col p-10 grow">
		<h1 class="text-[#0A1C7D] font-semibold text-center text-2xl">Activate Dealership</h1>

		<div class="w-ful flex flex-col my-10 mb-40">
			<div class="flex justify-between items-center">
				<label for="">Activation Code</label>
			</div>

			<input
				v-model="activationCode"
				type="text"
				class="input-bb"
				placeholder="Enter your first bill number."
				@input="activate.reset()"
				:disabled="activate.isPending.value"
			/>
		</div>

		<p v-show="activate.error.value" class="self-center text-red-500">
			{{ errToMessage(activate.error.value) }}
		</p>

		<button
			class="btn-primary"
			@click="
				activate.mutate({
					body: {
						dealership_activation_code: activationCode,
					},
				})
			"
			:disabled="activate.isPending.value"
		>
			Activate Dealership
		</button>

        <Loader v-if="activate.isPending.value" />
	</div>
</template>
