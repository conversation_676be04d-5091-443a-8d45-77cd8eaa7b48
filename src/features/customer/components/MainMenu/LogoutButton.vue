<script setup lang="ts">
import { logoutUser } from '@/api';
import { useAuthStore } from '~/login';
import { toLoginRoute } from '~/login/routes';
import { useRouter } from 'vue-router';

const router = useRouter();
const authStore = useAuthStore();
const logOut = async () => {
	try {
		await logoutUser();
		authStore.unsetToken();
		toLoginRoute(router);
	} catch (e) {
		console.error(e);
	}
};
</script>

<template>
	<div>
		<p class="text-center text-[#0A1C7D] text-lg" @click="logOut">Logout</p>
	</div>
</template>
