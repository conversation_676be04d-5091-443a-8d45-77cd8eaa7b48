<script setup lang="ts">
import { activateDealershipMutation, updateUserNameMutation } from '@/api';
import { useAuthStore } from '~/login';
import { useMutation } from '@tanstack/vue-query';
import { ref } from 'vue';
import { errToMessage } from '@/api';
import Loader from '@/components/Loader.vue';
import InputField from '@/components/InputField.vue';

const authStore = useAuthStore();

const name = ref('');

const activate = useMutation({
	...updateUserNameMutation(),
	onSuccess: async () => {
		await authStore.refreshUser();
	},
});
</script>

<template>
	<div class="flex flex-col grow">
		<h1 class="page-title">One Small Step</h1>

		<InputField
			label="Name"
			help="Enter your name."
			:disabled="activate.isPending.value"
			v-model="name"
			@input="activate.reset()"
		/>

		<p v-show="activate.error.value" class="self-center text-red-500">
			{{ errToMessage(activate.error.value) }}
		</p>

		<button
			class="btn-primary mt-auto"
			@click="
				activate.mutate({
					body: {
						name,
					},
				})
			"
			:disabled="activate.isPending.value"
		>
			Continue
		</button>

		<Loader v-if="activate.isPending.value" />
	</div>
</template>
