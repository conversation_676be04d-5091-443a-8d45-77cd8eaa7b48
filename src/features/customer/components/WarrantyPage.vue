<script lang="ts" setup>
import { toMainMenu } from '..';
import { useRoute, useRouter } from 'vue-router';
import { getCustomerStockOptions } from '@/api';
import { useQuery } from '@tanstack/vue-query';
import DefaultLayout from '@/components/DefaultLayout.vue';
import WarrantyReadonly from './WarrantyPage/WarrantyReadonly.vue';
import ApiHandler from '@/components/ApiHandler.vue';

const router = useRouter();
const route = useRoute();

const query = useQuery(
	getCustomerStockOptions({
		path: {
			stock: route.params.warrantyId as any,
		},
	})
);
</script>

<template>
	<DefaultLayout>
		<div class="flex justify-between flex-col p-10 grow">
			<div class="">
				<h1 class="text-[#0A1C7D] font-semibold text-center text-2xl mb-5">Warranty Information</h1>
			</div>
			<ApiHandler :query="query" bg-loader v-slot="{ data }">
				<WarrantyReadonly :warranty="data.data" />

				<div v-if="data && route.query.success" class="flex justify-center items-center flex-col gap-1 mt-5">
					<span class="text-center text-[#00703C] text-2xl font-bold">Congratulations</span>

					<p class="text-[#00703C] text-center">The Warranty of your battery has been registered</p>

					<span class="text-center text-[#00703C] text-2xl font-semibold">Successfully</span>

					<p class="text-[#7b7a7a] text-center">Check your Whatsapp & SMS box for Confirmation Messages</p>
				</div>
			</ApiHandler>

			<div class="flex flex-col items-stretch mt-auto gap-3">
				<div class="flex justify-center gap-5">
					<button class="btn-secondary" @click="toMainMenu(router)">Done</button>
				</div>
			</div>
		</div>
	</DefaultLayout>
</template>
