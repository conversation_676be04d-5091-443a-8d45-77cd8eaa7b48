<script setup lang="ts">
import { useRouter } from 'vue-router';
import { toWarranty, toWarrantyCheck } from '~/customer';
import { toRegisterComplaint, toComplaintList } from '~/complaint';
import DefaultLayout from '@/components/DefaultLayout.vue';
import LogoutButton from './MainMenu/LogoutButton.vue';
import { branding, isPartner, isFamily } from '@/utils';
import { useAuthStore } from '~/login';
import ActivateDealership from './MainMenu/ActivateDealership.vue';
import ActivateCustomer from './MainMenu/ActivateCustomer.vue';

const router = useRouter();
const authStore = useAuthStore();
</script>

<template>
	<DefaultLayout>
		<div class="p-10 flex flex-col grow gap-5">
			<template v-if="isPartner && !authStore.user?.dealership_activated_at">
				<ActivateDealership />
			</template>
			<template v-else-if="isFamily && !authStore.user?.name">
				<ActivateCustomer />
			</template>
			<template v-else>
				<div class="mainMneuItems" @click="toWarranty(router)">Register Warranty Online</div>
				<!-- <div class="mainMneuItems disabled">Buy Extended Warranty</div> -->
				<div class="mainMneuItems" @click="toWarrantyCheck(router)">Check Warranty Status</div>
				<div class="mainMneuItems" @click="toRegisterComplaint(router)">Register Your Complaint</div>
				<div class="mainMneuItems" @click="toComplaintList(router)">Check Complaint Status</div>
			</template>

			<LogoutButton class="mt-auto" />

			<div
				class="fixed bottom-20 left-1/2 transform -translate-x-1/2 w-36 h-36 bg-contain bg-center bg-no-repeat opacity-30"
				:style="`background-image: url('${branding.logoImage}')`"
			></div>
		</div>
	</DefaultLayout>
</template>
