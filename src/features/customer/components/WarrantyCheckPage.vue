<script lang="ts" setup>
import { listCustomerStocksOptions } from '@/api';
import { toWarrantyView } from '..';
import { useRouter } from 'vue-router';
import { useQuery } from '@tanstack/vue-query';
import WarrantyReadonly from './WarrantyPage/WarrantyReadonly.vue';
import DefaultLayout from '@/components/DefaultLayout.vue';
import ApiHandler from '@/components/ApiHandler.vue';

const router = useRouter();

const query = useQuery({
	...listCustomerStocksOptions(),
});
</script>

<template>
	<DefaultLayout>
		<div class="flex justify-between flex-col p-10 grow">
			<div class="">
				<h1 class="page-title">Check Warranty</h1>

				<ApiHandler :query="query" bg-loader v-slot="{ data }">
					<p v-if="data.data.length === 0" class="text-center text-xl font-semibold">
						No warranties registered
					</p>
					<WarrantyReadonly v-if="data.data.length === 1" :warranty="data.data[0]" />
					<template v-else-if="data.data.length > 1">
						<div class="flex flex-row w-full mb-5 justify-between">
							<h1 class="heading mr-3 font-semibold">Product</h1>
							<p class="font-semibold">Expiry Date</p>
						</div>
						<div v-for="stock of data.data" class="flex flex-row w-full mb-5 justify-between">
							<h1 class="heading mr-3 font-semibold">{{ stock?.product?.name }}</h1>
							<p class="font-semibold" @click="toWarrantyView(router, stock.id)">
								{{ stock?.warranty_end ? new Date(stock?.warranty_end).toLocaleDateString() : '-' }}
							</p>
						</div>
					</template>
				</ApiHandler>
			</div>
		</div>
	</DefaultLayout>
</template>
