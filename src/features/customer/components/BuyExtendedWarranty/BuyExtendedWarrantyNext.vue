<script setup lang="ts">
import { useRouter } from 'vue-router';
import { buyWarranty } from '../../steps';

const router = useRouter();
const emits = defineEmits<{
    step: [buyWarranty];
}>();

const mainMenu = () => {
    router.push({name: 'menu'})
}

const backPage = () => {
    emits('step', buyWarranty.BuyExtendedWarrantyBack);
}
const buyNow = () => {
    emits('step', buyWarranty.BuyExtendedWarrantySuccessful);
}
const cancel = () => {
    emits("step", buyWarranty.BuyExtendedWarrantyBack)
}
</script>

<template>
     <div class="mx-10 my-10">
        <h1 class="text-[#00703C] text-center text-2xl mt-16 font-semibold">Buy Extended Warranty</h1>

        <div class="w-full mt-10">
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Serial No.:</h1>
                <p class="font-semibold">250108283</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Battery Type:</h1>
                <p class="font-semibold">INVERTER BATTERY</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Model Name:</h1>
                <p class="font-semibold">RAB 25000 XL</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Date of Sale / Registration:</h1>
                <p class="font-semibold">01/01/2025</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Warranty till date:</h1>
                <p class="font-semibold">31/12/2007</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Warranty:</h1>
                <p class="font-bold">36 Months Free Replacement + 24 Months Pro Rata</p>
            </div>
        </div>

        <div class="flex justify-center items-center flex-col mt-16 gap-1">
            <span class="text-center text-[#00703C] text-2xl font-semibold">Extended Warranty Offer</span>

            <p class="text-[#00703C] text-center font-semibold">Get additional 12 Months Free Replacement
                Warranty by paying Rs.1000 only
            </p>
        </div>

        <div class="mainMneuItems text-2xl mt-10" @click="buyNow">
            BUY NOW
        </div>

        <div class="mt-14 flex justify-between">
            <button class="btn-secondary" @click="backPage">Back Page</button>
            <button class="btn-secondary" @click="mainMenu">Main Menu</button>
        </div>
    </div>
</template>

<style scoped>
.input{
    border-bottom: 2px solid #7a7a7a;
    margin-bottom: 20px;
    font-size: 16px;
}
label{
    color: #7a7a7a;
    font-size: 16px;
}
</style>