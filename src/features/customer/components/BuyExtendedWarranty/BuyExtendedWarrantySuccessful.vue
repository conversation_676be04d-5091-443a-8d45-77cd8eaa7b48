<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const mainMenu = () => {
    router.push({name: 'menu'})
}
const cancel = () => {
    router.push({name: "menu"})
}
</script>

<template>
    <div class="mx-10 my-10">
        <h1 class="text-[#00703C] text-center text-2xl mt-16 font-semibold">Buy Extended Warranty</h1>

        <div class="w-full mt-10">
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Serial No.:</h1>
                <p class="font-semibold">250108283</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Battery Type:</h1>
                <p class="font-semibold">INVERTER BATTERY</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Model Name:</h1>
                <p class="font-semibold">RAB 25000 XL</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Date of Sale / Registration:</h1>
                <p class="font-semibold">01/01/2025</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Warranty till date:</h1>
                <p class="font-semibold">31/12/2027</p>
            </div>
            <div class="flex flex-row w-full mb-3">
                <h1 class="mr-3">Warranty:</h1>
                <p class="font-bold text-[#00703C]">48 Months Free Replacement + 12 Months Pro Rata</p>
            </div>
        </div>

        <div class="flex justify-center items-center flex-col mt-16 gap-1">
            <span class="text-center text-[#00703C] text-2xl font-bold">CONGRATULATIONS</span>
            <p class="text-[#00703C] text-center text-xl font-semibold">The Extended Warranty of your battery has been registered</p>
            <span class="text-center text-[#00703C] text-2xl font-bold">SUCCESSFULLY</span> 
        </div>
        
        <div class="mt-10">
            <p class="text-[#7a7a7a] text-center">Check your Whatsapp & SMS box for Confirmation Messages</p>
        </div>

        <button class="btn btn-primary mt-10" @click="mainMenu">
            Main Menu
        </button>
    </div>

    

</template>

<style scoped>
.input{
    border-bottom: 2px solid #7a7a7a;
    margin-bottom: 20px;
    font-size: 16px;
}
label{
    color: #7a7a7a;
    font-size: 16px;
}
</style>