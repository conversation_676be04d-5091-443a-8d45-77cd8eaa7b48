<script lang="ts" setup>
import { buyWarranty } from '../steps';
import { ref } from 'vue';
import BuyExtendedWarrantyBack from './BuyExtendedWarranty/BuyExtendedWarrantyBack.vue';
import BuyExtendedWarrantyNext from './BuyExtendedWarranty/BuyExtendedWarrantyNext.vue';
import BuyExtendedWarrantySuccessful from './BuyExtendedWarranty/BuyExtendedWarrantySuccessful.vue';
import DefaultLayout from '@/components/DefaultLayout.vue';

const currentStep = ref(buyWarranty.BuyExtendedWarrantyBack);
const onStepChange = (step: buyWarranty) => {
	currentStep.value = step;
};
</script>

<template>
	<DefaultLayout>
		<BuyExtendedWarrantyBack v-if="currentStep === buyWarranty.BuyExtendedWarrantyBack" @step="onStepChange" />
		<BuyExtendedWarrantyNext v-else-if="currentStep === buyWarranty.BuyExtendedWarrantyNext" @step="onStepChange" />
		<BuyExtendedWarrantySuccessful
			v-else-if="currentStep === buyWarranty.BuyExtendedWarrantySuccessful"
			@step="onStepChange"
		/>
	</DefaultLayout>
</template>
