<script lang="ts" setup>
import { ref } from 'vue';
import EnterPhoneNumber from './partials/EnterPhoneNumber.vue';
import VerifyPhoneNumber from './partials/VerifyPhoneNumber.vue';
import { useAuthStore } from '../authStore';
import type { User } from '@/api';
import { toMainMenu } from '~/customer';
import { useRouter } from 'vue-router';

enum Steps {
	PhoneNumber = 1,
	Otp = 2,
}

const step = ref(Steps.PhoneNumber);

const phoneNumber = ref('');
const router = useRouter();

const authStore = useAuthStore();
const onLogin = (token: string, user: User) => {
	authStore.setToken(token, user);
	toMainMenu(router);
};
</script>

<template>
		<EnterPhoneNumber v-if="step === Steps.PhoneNumber" v-model="phoneNumber" @next="step = Steps.Otp" />
		<VerifyPhoneNumber v-if="step === Steps.Otp" v-model="phoneNumber" @back="step = Steps.PhoneNumber"
			@success="(v) => onLogin(v.access_token, v.user)" />

</template>

<style scoped>

</style>
