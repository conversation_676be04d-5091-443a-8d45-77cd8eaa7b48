<script setup lang="ts">
import { ref } from 'vue';

const emits = defineEmits<{
	submit: [];
}>();

const otp = defineModel<string>({
	required: true,
	default: '',
});

const OTP_LENGTH = 4;

const attemptFill = (target: any, index: number, value: string, focus = true) => {
	if (!(target instanceof HTMLInputElement)) {
		return;
	}
	console.log('fill', target, index, 'new', value, 'old', target.value);
	if (!['0', '1', '2', '3', '4', '5', '6', '7', '8', '9' as const].includes(value)) {
		return;
	}
	if (index < 1 || index > OTP_LENGTH) {
		return;
	}
	otp.value = otp.value.slice(0, index - 1) + value + otp.value.slice(index);
	target.value = value;
	if (focus && target.nextElementSibling && target.nextElementSibling instanceof HTMLInputElement) {
		target.nextElementSibling.focus();
	}
};

const handleOtpKey = (event: KeyboardEvent, index: number) => {
	console.log('key', event, index);

	if (!(event.target instanceof HTMLInputElement)) {
		return;
	}

	if (event.key === 'Backspace') {
		otp.value = otp.value.slice(0, index - 1) + '' + otp.value.slice(index);
		if (event.target.value) {
			event.target.value = '';
		} else if (
			event.target.previousElementSibling &&
			event.target.previousElementSibling instanceof HTMLInputElement
		) {
			event.target.previousElementSibling.focus();
			if (!event.target.value) {
				event.target.previousElementSibling.value = '';
			}
		}
	} else if (
		event.key === '0' ||
		event.key === '1' ||
		event.key === '2' ||
		event.key === '3' ||
		event.key === '4' ||
		event.key === '5' ||
		event.key === '6' ||
		event.key === '7' ||
		event.key === '8' ||
		event.key === '9'
	) {
		attemptFill(event.target, index, event.key);
	} else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
		if (event.key === 'ArrowLeft' && event.target.previousElementSibling instanceof HTMLInputElement) {
			event.target.previousElementSibling.focus();
		} else if (event.key === 'ArrowRight' && event.target.nextElementSibling instanceof HTMLInputElement) {
			event.target.nextElementSibling.focus();
		}
	} else if (event.key === 'Enter') {
		event.target.blur();
		emits('submit');
	}

	if (!(event.key === 'v' && event.ctrlKey)) {
		event.preventDefault();
		event.stopPropagation();
	}
};

const handleOtpInput = (event: Event, index: number) => {
	if (event instanceof InputEvent && event.data) {
		const otpNumbers = event.data.replace(/[^0-9]/g, '');
		console.log('input', event, index, 'data', event.data, 'otp ', otpNumbers);
		for (let i = 0; i < otpNumbers.length; i++) {
			attemptFill(otpInputs.value?.[i], i + 1, otpNumbers[i], false);
			otpInputs.value?.[i]?.blur();
		}
		otp.value = otpNumbers.slice(0, OTP_LENGTH);
	}
};

const otpInputs = ref<HTMLInputElement[]>();
const handlePaste = (event: ClipboardEvent) => {
	console.log('paste', event);
	event.preventDefault();
	const pastedData = event.clipboardData?.getData('text');
	if (!pastedData) return;

	// Only take numeric characters
	const numericData = pastedData.replace(/[^0-9]/g, '');
	otp.value = numericData.slice(0, OTP_LENGTH);

	for (const [idx, input] of otpInputs.value?.entries() ?? []) {
		console.log('Set input', idx, input, numericData[idx]);
		input.value = numericData[idx] ?? '';
		if (idx === numericData.length - 1) input.focus();
	}
};
</script>

<template>
	<div class="flex justify-center items-center mt-8 mb-16">
		<input
			v-for="n in OTP_LENGTH"
			:key="n"
			ref="otpInputs"
			type="text"
			inputmode="numeric"
			:maxlength="OTP_LENGTH"
			:class="[
				'w-otp border-0 border-b-2 border-otp mx-2.5 text-center text-xl focus:outline-none',
				n === 1 ? 'cursor-pointer pointer-events-auto' : 'cursor-not-allowed',
				`otp-${n}`,
			]"
			@keydown="handleOtpKey($event, n)"
			@input="handleOtpInput($event, n)"
			@paste="handlePaste($event)"
		/>
	</div>
</template>
