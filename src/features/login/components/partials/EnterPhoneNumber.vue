<script lang="ts" setup>
import { type Agreeable, errToMessage, getAgreeablesOptions, sendOtpMutation } from '@/api';
import { ref, shallowRef } from 'vue';
import Logo from '@/components/Logo.vue';
import { useMutation, useQuery } from '@tanstack/vue-query';

const emits = defineEmits<{
	next: [];
}>();

const phoneNumber = defineModel<string>({
	required: true,
});

const error = ref('');
const showConfirmation = ref(false);

const agreeables = useQuery({
	...getAgreeablesOptions(),
	staleTime: 60 * 1000,
});

const agreed = ref<string[]>([]);

const displayedAgreeable = shallowRef<Agreeable>();

const sendOtp = useMutation({
	...sendOtpMutation(),
	onSuccess: () => {
		emits('next');
	},
	onSettled: () => {
		showConfirmation.value = false;
	},
});

const toggleConfirmation = () => {
	if (!phoneNumber.value.match(/^[0-9]{10}$/)) {
		error.value = 'Please enter a valid phone number';
		return;
	}
	if (!showConfirmation.value) {
		showConfirmation.value = true;
		return false;
	}

	sendOtp.mutate({
		body: {
			phone_number: phoneNumber.value,
		},
	});
};
</script>

<template>
	<div class="p-10 min-h-screen flex flex-col">
		<Logo />
		<h1 class="text-3xl text-center mt-20 mb-5">Enter your phone number</h1>
		<p class="text-[#313131] text-xl text-center">We need to verify your number.</p>

		<div class="w-full mt-20 flex flex-col justify-center items-center">
			<div class="relative">
				<input
					type="text"
					v-model="phoneNumber"
					placeholder="your phone number"
					class="bg-[#f0f0f0] text-[#313131] text-lg w-full h-14 px-5 rounded-lg ps-14"
					maxlength="10"
					@keypress="
						error = '';
						sendOtp.reset();
					"
				/>
				<div class="absolute left-0 top-0 h-full flex flex-col justify-center ml-2 text-[#313131] text-lg">
					<span>+91</span>
				</div>
			</div>
		</div>
		<div class="mt-10 flex flex-col justify-center self-center items-start gap-5">
			<div v-for="agreeable in agreeables.data.value ?? []">
				<div :key="agreeable.id" class="flex flex-nowrap items-start gap-5">
					<div>
						<input type="checkbox" v-model="agreed" :value="agreeable.id" :id="agreeable.id" />
					</div>
					<label
						:for="agreeable.id"
						:class="{
							'text-red-500': !!sendOtp.error.value?.response?.data?.errors?.[agreeable.id],
						}"
					>
						I agree to the
						<a href="javascript:void(0)" class="underline" @click="displayedAgreeable = agreeable">
							{{ agreeable.label }}
						</a>
					</label>
				</div>
			</div>
		</div>

		<Transition name="slide-up">
			<div
				v-if="showConfirmation"
				class="fixed top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-50"
			>
				<div class="bg-[#f0f0f0] w-[250px] h-[120px] rounded-xl flex items-center justify-center flex-col">
					<div class="my-2 text-center">
						<div>+91 {{ phoneNumber }}</div>
						<p class="text-[#7d7d7d] text-md">Is this the correct number?</p>
					</div>
					<div class="border-t-2 border-white w-full h-full flex flex-row">
						<template v-if="sendOtp.isPending.value">
							<div class="w-full flex justify-stretch items-stretch border-r-2 border-white">
								<button type="button" disabled class="text-[#0A1C7D] text-lg grow">
									Sending OTP...
								</button>
							</div>
						</template>
						<template v-else>
							<div class="w-[50%] flex justify-center items-center border-r-2 border-white">
								<button type="button" @click="showConfirmation = false" class="text-[#0A1C7D] text-lg">
									Edit
								</button>
							</div>
							<div class="w-[50%] flex justify-center items-center">
								<button
									type="submit"
									@click="
										sendOtp.mutate({
											body: {
												phone_number: phoneNumber,
												...agreed.reduce(
													(acc, curr) => ({ ...acc, [curr]: true }),
													{} as Record<string, boolean>
												),
											},
										})
									"
									class="text-[#0A1C7D] text-lg"
								>
									Yes
								</button>
							</div>
						</template>
					</div>
				</div>
			</div>
		</Transition>

		<Transition name="slide-up">
			<div
				v-if="displayedAgreeable"
				class="fixed top-0 left-0 right-0 bottom-0 bg-white bg-opacity-50 flex flex-col items-center"
			>
				<div class="bg-[#f0f0f0] max-w-md m-10 rounded-xl flex flex-col justify-start items-center">
					<!-- Heading, followed by content, full screen -->
					<div class="w-full flex justify-between items-center border-b-2 border-white p-5 max-h-[10vh]">
						<h6>
							{{ displayedAgreeable.label }}
						</h6>
						<div>
							<button
								type="button"
								@click="displayedAgreeable = undefined"
								class="text-[#0A1C7D] text-lg"
							>
								Close
							</button>
						</div>
					</div>
					<div class="my-5 max-h-[75vh] grow p-5 overflow-y-auto">
						<div v-html="displayedAgreeable.content"></div>
					</div>
				</div>
			</div>
		</Transition>

		<div class="mt-auto">
			<p v-if="error || sendOtp.error.value" class="text-red-500 text-center mt-4">
				{{ errToMessage(error || sendOtp.error.value) }}
			</p>
			<button
				type="button"
				class="btn btn-primary mt-5"
				@click="toggleConfirmation"
				:disabled="agreeables.isPending.value"
			>
				Next
			</button>
		</div>
	</div>
</template>

<style scoped>
.slide-up-enter-active,
.slide-up-leave-active {
	transition: all 0.3s ease-out;
}

.slide-up-enter-from {
	transform: translateY(20px);
	opacity: 0;
}

.slide-up-leave-to {
	transform: translateY(20px);
	opacity: 0;
}
</style>
