<script lang="ts" setup>
import { ref } from 'vue';
import { errToMessage, loginWithOtpMutation, sendOtpMutation, type LoginWithOtpResponse } from '@/api';
import { useMutation } from '@tanstack/vue-query';
import OtpInput from './OtpInput.vue';
import Logo from '@/components/Logo.vue';
import Loader from '@/components/Loader.vue';
import { appType } from '@/utils';

const emits = defineEmits<{
	success: [LoginWithOtpResponse];
	back: [];
}>();

const phoneNumber = defineModel<string>({
	required: true,
});

const otp = ref('');

const login = useMutation({
	...loginWithOtpMutation(),
	onSuccess(data) {
		emits('success', data);
	},
	onMutate: async (req) => {
		if (!req.body.phone_number || !req.body.otp) {
			throw new Error('Please enter a valid phone number and OTP');
		}
		return req;
	},
});

const resendOtp = useMutation({
	...sendOtpMutation(),
});
</script>

<template>
	<div class="p-10 min-h-screen flex flex-col">
		<Logo />
		<h1 class="text-3xl text-center mt-20 mb-10">Verify your phone number</h1>
		<p class="text-[#313131] text-lg text-center">
			Enter the 4-digit code we sent by SMS to <br />+91 {{ phoneNumber }}
		</p>

		<OtpInput
			v-model="otp"
			@submit="
				login.mutate({
					body: {
						otp: otp,
						phone_number: phoneNumber,
						app_type: appType,
					},
				})
			"
		/>
		<button
			type="button"
			class="btn btn-primary mt-5 mb-10 flex justify-center items-center"
			:disabled="login.isPending.value || resendOtp.isPending.value"
			@click="
				login.mutate({
					body: {
						otp: otp,
						phone_number: phoneNumber,
						app_type: appType,
					},
				})
			"
		>
			<span v-if="login.isPending.value" class="inline-flex items-center gap-2">
				Loading...
			</span>
			<span v-else> Verify OTP </span>
		</button>
		<p v-if="login.error.value || resendOtp.error.value" class="text-red-500 text-center">
			{{ errToMessage(login.error.value || resendOtp.error.value) }}
		</p>
		<div class="flex justify-center">
			<button
				type="button"
				class="text-center text-[#0A1C7D] text-sm"
				:disabled="resendOtp.isPending.value"
				@click="
					resendOtp.mutate({
						body: {
							phone_number: phoneNumber,
						},
					});
					login.reset();
				"
			>
				{{ resendOtp.isPending.value ? 'Resending OTP...' : "Didn't receive a verification code?" }}
			</button>
		</div>
	</div>
</template>

<style scoped>
.slide-up-enter-active,
.slide-up-leave-active {
	transition: all 0.3s ease-out;
}

.slide-up-enter-from {
	transform: translateY(20px);
	opacity: 0;
}

.slide-up-leave-to {
	transform: translateY(20px);
	opacity: 0;
}
</style>
