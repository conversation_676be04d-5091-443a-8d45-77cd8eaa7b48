import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { useEventBus } from '@vueuse/core';
import type { AccessTokenEvent } from '@/types';
import { getUser, type User } from '@/api';

export const useAuthStore = defineStore(
	'auth',
	() => {
		const accessToken = ref<string>();
		const user = ref<User>();

		const isLoggedIn = computed(() => !!accessToken.value);

		const { emit: emitAccessTokenEvent, on: onAccessTokenEvent } = useEventBus<AccessTokenEvent>('access-token');

		const setToken = (token: string, theUser: User) => {
			accessToken.value = token;
			user.value = theUser;
			emitAccessTokenEvent({
				type: 'access-token-set',
				token,
				user: theUser,
			});
		};

		const unsetToken = () => {
			if (!accessToken.value) {
				user.value = undefined;
				return;
			}
			accessToken.value = undefined;
			user.value = undefined;
			emitAccessTokenEvent({
				type: 'access-token-unset',
			});
		};

		onAccessTokenEvent((v) => {
			if (v.type === 'access-token-unset-request') {
				unsetToken();
			}
		});

		const refreshUser = async () => {
			if (!user.value?.id) {
				unsetToken();
				return;
			}
			try {
				const { data } = await getUser({
					path: {
						user: user.value.id,
					},
				});

				user.value = data;
			} catch (e) {
				unsetToken();
			}
		};

		return {
			accessToken,
			user,
			isLoggedIn,
			setToken,
			unsetToken,
			refreshUser,
		};
	},
	{
		persist: {
			storage: localStorage,
			pick: ['accessToken', 'user'],
			afterHydrate: (ctx) => {
				const { emit: emitAccessTokenEvent } = useEventBus<AccessTokenEvent>('access-token');
				if (ctx.store.accessToken && ctx.store.user) {
					emitAccessTokenEvent({
						type: 'access-token-set',
						token: ctx.store.accessToken,
						user: ctx.store.user,
					});
					ctx.store.refreshUser();
				} else {
					ctx.store.unsetToken();
				}
			},
		},
	}
);
