import type { Router, RouteRecordRaw } from 'vue-router';
import RegisterComplaintPage from './components/RegisterComplaintPage.vue';
import ComplaintListPage from './components/ComplaintListPage.vue';
import ComplaintShowPage from './components/ComplaintShowPage.vue';

export default function (): RouteRecordRaw[] {
	return [
		{
			name: 'register-complaint',
			component: RegisterComplaintPage,
			path: '/register-complaint',
		},
		{
			name: 'complaint-list',
			component: ComplaintListPage,
			path: '/complaint-list',
		},
		{
			name: 'complaint-show',
			component: ComplaintShowPage,
			path: '/complaint-show/:id',
			meta: { page: 2 },
		},
	];
}

export const toRegisterComplaint = (router: Router) => router.push({ name: 'register-complaint' });
export const toComplaintList = (router: Router) => router.push({ name: 'complaint-list' });
export const toComplaintShow = (router: Router, id: number) => router.push({ name: 'complaint-show', params: { id } });
