<script setup lang="ts">
import type { Complaint } from '@/api';

const props = defineProps<{
	complaint: Complaint;
}>();
</script>

<template>
	<div class="w-full mt-10">
		<div class="flex flex-col w-full mb-3">
			<h1 class="mr-3 heading">Serial No.:</h1>
			<p class="font-semibold">{{ complaint.stock?.serial_number }}</p>
		</div>
		<div v-for="(value, key) of complaint.stock?.product?.specifications" class="flex flex-col w-full mb-5">
			<h1 class="heading mr-3">{{ key }}:</h1>
			<p class="font-semibold">{{ value }}</p>
		</div>
		<div class="flex flex-col w-full mb-3">
			<h1 class="heading mr-3">Model Name:</h1>
			<p class="font-semibold">{{ complaint.stock?.product?.name }}</p>
		</div>
		<div class="flex flex-col w-full mb-3">
			<h1 class="heading mr-3">Warranty Start Date:</h1>
			<p class="font-semibold">
				{{
					complaint.stock?.warranty_start
						? new Date(complaint.stock?.warranty_start).toLocaleDateString()
						: '-'
				}}
			</p>
		</div>
		<div class="flex flex-col w-full mb-3">
			<h1 class="heading mr-3">Warranty End Date:</h1>
			<p class="font-semibold">
				{{ complaint.stock?.warranty_end ? new Date(complaint.stock?.warranty_end).toLocaleDateString() : '-' }}
			</p>
		</div>
		<div class="flex flex-col w-full mb-3">
			<h1 class="heading mr-3">Ticket No.:</h1>
			<p class="font-semibold">{{ complaint.id.toString().padStart(4, '0') }}</p>
		</div>
		<div class="flex flex-col w-full mb-3">
			<h1 class="heading mr-3">Date:</h1>
			<p class="font-semibold">{{ new Date(complaint.created_at).toLocaleDateString() }}</p>
		</div>
		<div class="flex flex-col w-full mb-3">
			<h1 class="heading mr-3">Status:</h1>
			<p class="font-semibold" :class="complaint.resolved_at ? 'text-green-600' : 'text-red-600'">
				{{
					complaint.resolved_at
						? 'Resolved on ' + new Date(complaint.resolved_at).toLocaleDateString()
						: 'In Process of resolution'
				}}
			</p>
		</div>
		<div class="flex flex-col w-full mb-3">
			<h1 class="heading mr-3">Query:</h1>
			<p class="font-semibold">
				{{ complaint.complaint }}
			</p>
		</div>
		<div v-if="complaint.resolution" class="flex flex-col w-full mb-3">
			<h1 class="heading mr-3">Resolution:</h1>
			<p class="font-semibold">
				{{ complaint.resolution }}
			</p>
		</div>
	</div>
</template>
