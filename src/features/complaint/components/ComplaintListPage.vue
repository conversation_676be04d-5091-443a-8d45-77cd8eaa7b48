<script lang="ts" setup>
import { listComplaintsOptions } from '@/api';
import ApiHandler from '@/components/ApiHandler.vue';
import DefaultLayout from '@/components/DefaultLayout.vue';
import { useQuery } from '@tanstack/vue-query';
import { useRouter } from 'vue-router';
import ComplaintView from './partials/ComplaintView.vue';
import { toComplaintShow } from '../routes';
import NavigateItem from '@/components/NavigateItem.vue';

const router = useRouter();

const query = useQuery({
	...listComplaintsOptions(),
});
</script>

<template>
	<DefaultLayout>
		<div class="flex flex-col p-10 grow">
			<h1 class="page-title">Check Complaint Status</h1>

			<ApiHandler :query="query" bg-loader v-slot="{ data }">
				<p v-if="data.data?.length === 0" class="text-center text-xl font-semibold">No complaints registered</p>
				<ComplaintView v-else-if="data.data?.length === 1" :complaint="data.data[0]" />
				<template v-else-if="data.data">
					<div class="flex flex-row w-full mb-5 justify-between">
						<h1 class="heading mr-3 font-semibold">Product</h1>
						<p class="font-semibold">Complaint Date</p>
					</div>
					<NavigateItem
						v-for="complaint of data.data"
						:title="complaint?.stock?.product?.name ?? '-'"
						:subtitle="new Date(complaint?.created_at).toLocaleDateString()"
						@click="toComplaintShow(router, complaint.id)"
					>
					</NavigateItem>
				</template>
			</ApiHandler>
		</div>
	</DefaultLayout>
</template>
