<script lang="ts" setup>
import DefaultLayout from '@/components/DefaultLayout.vue';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { z } from 'zod';
import { createComplaint, listCustomerStocksOptions } from '@/api';
import { toComplaintShow } from '../routes';
import { useRouter } from 'vue-router';
import { useAuthStore } from '~/login';
import InputField from '@/components/InputField.vue';
import SelectField from '@/components/SelectField.vue';
import { useQuery } from '@tanstack/vue-query';
import { setValidationErrors } from '@/utils';

const authStore = useAuthStore();

const query = useQuery({
	...listCustomerStocksOptions(),
});

const { handleSubmit, isSubmitting, errors, defineField } = useForm({
	validationSchema: toTypedSchema(
		z.object({
			complaint: z.string(),
			stock_id: z.number(),
			user_id: z.number(),
		})
	),
	initialValues: {
		user_id: authStore.user?.id,
	},
});
const [complaintField, complaintFieldProps] = defineField('complaint');
const [stockIdField, stockIdFieldProps] = defineField('stock_id');

const router = useRouter();

const submit = handleSubmit(async (body, ctx) => {
	try {
		const { data } = await createComplaint({ body });
		toComplaintShow(router, data.data.id);
	} catch (e) {
		if (!setValidationErrors(ctx, e)) {
			throw e;
		}
	}
});
</script>

<template>
	<DefaultLayout>
		<div class="flex flex-col p-10 grow">
			<h1 class="page-title">Register Complaint</h1>

			<div class="flex flex-col items-stretch gap-5">
				<SelectField
					v-model="stockIdField"
					v-bind="stockIdFieldProps"
					label="Product"
					help="Select the product for which the complaint is being registered."
					:error="errors.stock_id"
					:options="
						query.data.value?.data.map((stock) => ({
							value: stock.id,
							label: `${stock.product?.name} - ${stock.warranty_start ? new Date(stock.warranty_start).toLocaleDateString() : '-'}`,
						})) || []
					"
					placeholder="Select Product"
				/>
				<InputField
					v-model="complaintField"
					v-bind="complaintFieldProps"
					label="Complaint"
					help="Describe your complaint in detail."
					textarea
					:error="errors.complaint"
					:disabled="!stockIdField"
				/>
			</div>

			<div class="flex justify-between mt-auto gap-10">
				<button class="btn-secondary" @click="router.back()">Go Back</button>
				<button class="btn-primary" @click="submit" :disabled="isSubmitting">Submit</button>
			</div>
		</div>
	</DefaultLayout>
</template>
