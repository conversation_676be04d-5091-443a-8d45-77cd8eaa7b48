<script lang="ts" setup>
import { getComplaintOptions } from '@/api';
import ApiHandler from '@/components/ApiHandler.vue';
import DefaultLayout from '@/components/DefaultLayout.vue';
import { useQuery } from '@tanstack/vue-query';
import { useRouter } from 'vue-router';
import ComplaintView from './partials/ComplaintView.vue';
import { toComplaintList } from '../routes';

const router = useRouter();

const query = useQuery({
	...getComplaintOptions({
		path: {
			id: router.currentRoute.value.params.id as any,
		},
	}),
});
</script>

<template>
	<DefaultLayout>
		<div class="flex justify-start flex-col p-10 grow">
			<h1 class="page-title">Check Complaint Status</h1>

			<ApiHandler :query="query" bg-loader v-slot="{ data }">
				<ComplaintView :complaint="data.data" />
			</ApiHandler>

			<div class="mt-auto flex justify-between self-center">
				<button class="btn-secondary" @click="toComplaintList(router)">Go Back</button>
			</div>
		</div>
	</DefaultLayout>
</template>
