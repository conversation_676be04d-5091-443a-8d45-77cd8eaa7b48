APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:pS6xeygBO9IOI0n7SV5O+y9vp1NKlIpnwj3GBmW7XWM=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

KAFKA_BROKER_LIST=localhost:9092

## SMS GATEWAY - SMSSweet
# SMS_PROVIDER='SMSSWEET' 
# SMS_API_URL='http://*************/send.aspx'
# SMS_USERNAME=smssweet
# SMS_PASSWORD=123456
# SMS_ROUTE_ID=3
# SMS_SENDER_ID=KATMAK

# SMS Gateway - PowersText
SMS_PROVIDER='POWERSTEXT' # 'POWERS_TEXT' | 'SMSSWEET'
SMS_API_URL='https://sms.akaal.biz/http-tokenkeyapi.php'
SMS_TOKEN_KEY='xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
SMS_ROUTE_ID=4

# SMS Template IDs
# TODO: Move tempates to database
SMS_SENDER_ID=SENDER
SMS_OTP_TEMPLATE_ID='1xxxxxxxxxxxx8'
SMS_WELCOME_TEMPLATE_ID='1xxxxxxxxxxxx8'
SMS_WARRANTY_CONFIRMATION_TEMPLATE_ID='1xxxxxxxxxxxx8'
SMS_EXTENDED_WARRANTY_CONFIRMATION_TEMPLATE_ID='1xxxxxxxxxxxx8'
SMS_COMPLAIN_TEMPLATE_ID='1xxxxxxxxxxxx8'
SMS_COMPLAIN_RESOLVED_TEMPLATE_ID='1xxxxxxxxxxxx8'