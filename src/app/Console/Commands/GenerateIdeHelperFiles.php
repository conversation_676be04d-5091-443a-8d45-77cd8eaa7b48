<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class GenerateIdeHelperFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ide-helper:custom';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate ide helper files for this project.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Create directory for IDE helper files
        if (!is_dir('vendor/_ide_helper')) {
            mkdir('vendor/_ide_helper', 0755, true);
        }

        // Generate models helper
        $this->info('Generating IDE helper for models...');
        $this->call('ide-helper:models', [
            '--nowrite' => true,
            '--filename' => 'vendor/_ide_helper/_ide_helper_models.php',
        ]);

        // Generate general helper
        $this->info('Generating general IDE helper...');
        $this->call('ide-helper:generate', [
            '--helpers' => true,
            'filename' => 'vendor/_ide_helper/_ide_helper.php',
        ]);

        // Generate eloquent helper
        $this->info('Generating eloquent IDE helper...');
        $this->call('ide-helper:eloquent');

        // Generate actions helper
        $this->info('Generating actions IDE helper...');
        $this->call('ide-helper:actions');

        // Move actions helper file to the correct location
        if (file_exists('_ide_helper_actions.php')) {
            rename('_ide_helper_actions.php', 'vendor/_ide_helper/_ide_helper_actions.php');
            $this->info('Moved actions helper file to vendor/_ide_helper/_ide_helper_actions.php');
        }

        $this->info('All IDE helper files generated successfully!');
    }
}
