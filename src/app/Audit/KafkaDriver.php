<?php

namespace App\Audit;

use App\Services\KafkaProducerService;
use OwenIt\Auditing\Contracts\Audit;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Contracts\AuditDriver;

class KafkaDriver implements AuditDriver
{
    public function audit(Auditable $model): ?Audit
    {
        app(KafkaProducerService::class)->audit('audits', $model->toAudit());

        $class = config('audit.implementation');
        return new $class;
    }

    public function prune(Auditable $model): bool
    {
        return false;
    }
}
