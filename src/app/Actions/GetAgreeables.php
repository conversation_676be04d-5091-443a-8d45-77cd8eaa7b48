<?php

namespace App\Actions;

use App\Data\Agreeable;
use Lorisleiva\Actions\Concerns\AsAction;

class GetAgreeables
{
    use AsAction;

    public function handle(): array
    {
        return [
            Agreeable::latest('terms_and_conditions', 'Terms and Conditions'),
            Agreeable::latest('privacy_policy', 'Privacy Policy'),
        ];
    }

    public function asController(): \Illuminate\Http\JsonResponse
    {
        return response()->json($this->handle());
    }
}
