<?php

namespace App\Actions\Sms;

use App\Models\Stock;
use App\Services\SmsService;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class SendWarrantyConfirmationSms
{
    use AsAction;

    public function __construct(protected SmsService $smsService)
    {
    }

    public function handle(Stock $stock): bool
    {
        if (!$stock->warrantee) {
            Log::error('Stock has no warrantee');
            return false;
        }

        if (!$stock->warrantee?->phone_number) {
            Log::error('Warrantee has no phone number');
            return false;
        }

        if (!$stock->warranty_end || !$stock->warranty_start) {
            Log::error('Stock has no warranty end or start');
            return false;
        }

        $this->smsService->sendSms($stock->warrantee->phone_number, <<<SMS
        Thank you for registering your product warranty! Your warranty is now active till {$stock->warranty_end->format('Y-m-d')}. Download the Ralicoan family app.
        RABTRY
        SMS, config('sms.templates.warranty_confirmation'));

        return true;
    }
}
