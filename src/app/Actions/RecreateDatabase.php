<?php

namespace App\Actions;

use App\Data\WarrantySpec;
use App\Enums\ProductStatus;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Stock;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class RecreateDatabase
{
    public function execute(): void
    {
        if (config('database.default') === ' mysql') {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        }

        Stock::truncate();
        OrderItem::truncate();
        Order::truncate();
        Product::truncate();

        if (config('database.default') === ' mysql') {
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }


        $dealer = User::isDealer()->firstOrFail();
        $customer = User::isCustomer()->firstOrFail();

        $productA = Product::create([
            'name' => 'RAB 25000 XL',
            'status' => ProductStatus::PUBLISHED,
            'description' => 'RAB 25000 XL is a 12V 200Ah battery with 4:00 Hrs* run time.',
            'price' => '12500.00',
            'specifications' => [
                'Battery Type' => 'Inverter Battery',
            ],
            'warranty' => WarrantySpec::from([
                'description' => '36 Months Free Replacement + 24 Months Pro Rata',
                'duration' => 36,
                'extension' => 24,
                'leeway' => 3,
            ])
        ]);

        $productB = Product::create([
            'name' => 'RAB 36000 XL',
            'status' => ProductStatus::PUBLISHED,
            'description' => 'RAB 36000 XL is a 12V 280Ah battery with 5:40 Hrs* run time.',
            'price' => '18900.00',
            'specifications' => [
                'Battery Type' => 'Inverter Battery',
            ],
            'warranty' => WarrantySpec::from([
                'description' => '36 Months Free Replacement + 24 Months Pro Rata',
                'duration' => 36,
                'extension' => 24,
                'leeway' => 3,
            ])
        ]);

        for ($i = 0; $i < 999; $i++) {
            $product = $i % 2 === 0 ? $productA : $productB;
            $product->stocks()->create([
                'serial_number' => 'SN' . Str::padLeft((string)$i, '3', '0'),
                'warranty' => $product->warranty,
                ...($i > 950 && $i < 975 ? [
                    'warrantee_id' => $customer->id,
                    'warranty_start' => now()->subWeeks($i - 950),
                    'warranty_end' => now()->subWeeks($i - 950)->addMonths($product->warranty->duration),
                ] : []),
            ]);
        }
    }
}
