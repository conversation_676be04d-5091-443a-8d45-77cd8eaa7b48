<?php

namespace App\Filament\Widgets;

use App\Models\Product;
use App\Models\Stock;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Customers', User::isCustomer()->count())
                ->description(
                    User::isCustomer()->where('created_at', '>', now()->subDays(7))->count()
                    . ' registered in last 7 days'
                ),
            Stat::make('Products', Product::count()),
            Stat::make('Stock', Stock::count())
                ->description(
                    Stock::where('created_at', '>', now()->subDays(7))->count()
                    . ' added in last 7 days'
                ),
            Stat::make('Warranties Registered', Stock::whereNotNull('warrantee_id')->count()),
        ];
    }
}
