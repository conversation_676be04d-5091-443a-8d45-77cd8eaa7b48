<?php

namespace App\Filament\Widgets;

use App\Services\IncompleteRegistrationService;
use Filament\Widgets\Widget;

class IncompleteRegistrations extends Widget
{
    protected static string $view = 'filament.widgets.incomplete-registrations';

    protected int | string | array $columnSpan = 'full';

    public function __construct(
        private IncompleteRegistrationService $incompleteRegistrationService
    ) {
        parent::__construct();
    }

    protected function getViewData(): array
    {
        return [
            'incompleteRegistrations' => $this->incompleteRegistrationService->getIncompleteRegistrations(),
            'totalCount' => $this->incompleteRegistrationService->getIncompleteRegistrationsCount(),
        ];
    }

    public static function getSort(): int
    {
        return 1; // Display this widget first
    }
}
