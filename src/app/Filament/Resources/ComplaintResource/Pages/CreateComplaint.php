<?php

namespace App\Filament\Resources\ComplaintResource\Pages;

use App\Data\ComplaintUpdate;
use App\Enums\ComplaintUpdateType;
use App\Filament\Resources\ComplaintResource;
use Carbon\Carbon;
use Filament\Resources\Pages\CreateRecord;
use Spatie\LaravelData\DataCollection;

class CreateComplaint extends CreateRecord
{
    protected static string $resource = ComplaintResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        return $data;
    }
}
