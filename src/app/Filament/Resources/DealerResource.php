<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DealerResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class DealerResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $label = 'Dealer';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->maxLength(255),
                Forms\Components\TextInput::make('phone_number')->maxLength(255)->tel()->required()->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('email')->email()->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('pincode')->required(),
                Forms\Components\TextInput::make('address')->required(),
                Forms\Components\TextInput::make('dealership_code')
                    ->maxLength(255)
                    ->default('D' . date('Y') . str_pad(random_int(0, 9999), 4, '0', STR_PAD_LEFT))
                    ->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('dealership_activation_code')->required()->label('First Bill Number')
                    ->helperText('The Dealer will use this code to activate their account.'),
                Forms\Components\DateTimePicker::make('dealership_activated_at')->readOnlyOn(['edit', 'view'])->hiddenOn('create')
                    ->helperText('When the account was activated via activation code in Ralicoan Dealer App.'),
                Forms\Components\KeyValue::make('meta')->helperText('Enter any meta data related to the dealer here, such as GST number.')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable(),
                Tables\Columns\TextColumn::make('email')->searchable(),
                Tables\Columns\TextColumn::make('phone_number')->searchable(),
                Tables\Columns\TextColumn::make('pincode')->searchable(),
                Tables\Columns\TextColumn::make('address')->searchable(),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDealers::route('/'),
            //            'create' => Pages\CreateDealer::route('/create'),
            'edit' => Pages\EditDealer::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->isDealer();
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            'Name' => $record->name,
            'Email' => $record->email,
        ];
    }

    public static function canCreate(): bool
    {
        return static::can('createDealer');
    }
}
