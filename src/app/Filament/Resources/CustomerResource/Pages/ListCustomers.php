<?php

namespace App\Filament\Resources\CustomerResource\Pages;

use App\Enums\Roles;
use App\Filament\Resources\CustomerResource;
use App\Models\User;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class ListCustomers extends ListRecords
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->mutateFormDataUsing(function (array $data): array {
                    $data['password'] = Hash::make(Str::password());
                    return $data;
                })
                ->after(function (User $user) {
                    $user->assignRole(Roles::CUSTOMER);
                }),
        ];
    }
}
