<?php

namespace App\Filament\Resources\CustomerResource\RelationManagers;

use App\Filament\Resources\StockResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StocksRelationManager extends RelationManager
{
    protected static string $relationship = 'warrantedStocks';

    public function form(Form $form): Form
    {
        return StockResource::form($form);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Warranties Claimed')
            ->recordTitleAttribute('warrantee_id')
            ->columns([
                Tables\Columns\TextColumn::make('serial_number')->searchable(),
                Tables\Columns\TextColumn::make('warranty_start')->sortable(),
                Tables\Columns\TextColumn::make('warranty_end')->sortable(),
                Tables\Columns\TextColumn::make('product.name')->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
//                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                //                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                //                Tables\Actions\BulkActionGroup::make([
                //                    Tables\Actions\DeleteBulkAction::make(),
                //                ]),
            ]);
    }
}
