<?php

namespace App\Filament\Resources\DealerResource\Pages;

use App\Filament\Resources\DealerResource;
use App\Models\User;
use App\Services\OtpService;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditDealer extends EditRecord
{
    protected static string $resource = DealerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('Login Assist')
                ->link()
                ->action(function (User $user) {
                    if (!$user->phone_number) {
                        Notification::make()->title('No phone number')->danger()->send();
                        return;
                    }

                    $otp = app(OtpService::class)->generateOtp($user->phone_number);

                    Notification::make()->title('Generated OTP is ' . $otp)->body('OTP will expire in 10 minutes')->success()->send();
                })
        ];
    }
}
