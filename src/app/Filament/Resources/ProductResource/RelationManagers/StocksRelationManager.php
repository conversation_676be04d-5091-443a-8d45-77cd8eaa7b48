<?php

namespace App\Filament\Resources\ProductResource\RelationManagers;

use App\Filament\Resources\StockResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Table;

class StocksRelationManager extends RelationManager
{
    protected static string $relationship = 'stocks';

    public function form(Form $form): Form
    {
        return StockResource::form($form);
    }

    public function table(Table $table): Table
    {
        return StockResource::table($table)->pushHeaderActions([
            CreateAction::make()
        ]);
    }
}
