<?php

namespace App\Filament\Resources;

use App\Enums\ProductStatus;
use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';

    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->required(),
                Forms\Components\Select::make('status')->options(ProductStatus::options())->required(),
                Forms\Components\TextInput::make('price')->required()->numeric()->prefix('₹')->minValue(0.01)->maxValue(42949672.95)
                    ->step(0.01),
                Forms\Components\Textarea::make('description')->required()->columnSpanFull(),
                Forms\Components\Fieldset::make('Warranty Details')->schema(static::warrantySpecSchema()),
                Forms\Components\KeyValue::make('specifications')
                    ->helperText('Enter specifications of battery. These are displayed to customers.')->columnSpanFull()
                    ->editableKeys(false)->addable(false)->deletable(false),
            ]);
    }

    public static function warrantySpecSchema(): array
    {
        return [
            Forms\Components\TextInput::make('warranty.description')->label('Description')->required()
                ->default('36 Months Free Replacement'),
            Forms\Components\TextInput::make('warranty.duration')->label('Duration')->numeric()->required()->default(1)->minValue(1)
                ->suffix('months')->helperText('Duration of the standard/base warranty in months.'),
            Forms\Components\TextInput::make('warranty.leeway_months')->label('Sales Leeway')->numeric()->required()->default(0)
                ->minValue(0)
                ->suffix('months')
                ->helperText('If the warranty is registered within this many months of sale of stock unit, the registered warranty will be based on date of registration and not date of sale.'),
            Forms\Components\TextInput::make('warranty.extension_months')->label('Extension')->numeric()->required()
                ->minValue(0)->default(0)
                ->suffix('months')->helperText('Extension of the standard/base warranty in months, if available.'),
        ];
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('status')->badge(),
                Tables\Columns\TextColumn::make('name')->searchable(),
                Tables\Columns\TextColumn::make('price')->sortable()->money('INR'),
                Tables\Columns\TextColumn::make('description')->searchable(),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')->options(
                    ProductStatus::options()
                )
            ])
            ->actions([
                Tables\Actions\EditAction::make()
            ])
            ->bulkActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\StocksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            //            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
