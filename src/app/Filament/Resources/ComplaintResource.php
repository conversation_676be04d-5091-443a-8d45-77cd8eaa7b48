<?php

namespace App\Filament\Resources;

use App\Data\ComplaintUpdate;
use App\Enums\ComplaintUpdateType;
use App\Enums\Roles;
use App\Filament\Resources\ComplaintResource\Pages;
use App\Models\Complaint;
use App\Models\User;
use Carbon\Carbon;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Spatie\LaravelData\DataCollection;

class ComplaintResource extends Resource
{
    protected static ?string $model = Complaint::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left';

    protected static ?string $recordTitleAttribute = 'id';

    public static function userSearch(Select $select, array $roles): Select
    {
        return $select
            ->getSearchResultsUsing(fn(string $search) => User::isAnyRole($roles)
                ->whereAny(['name', 'email', 'phone_number'], 'ilike', "%{$search}%")
                ->limit(50)->pluck('name', 'id')->toArray())
            ->getOptionLabelUsing(fn($value) => User::find($value)?->name ?? 'User not found');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Textarea::make('complaint')
                    ->required()
                    ->columnSpanFull(),

                Select::make('stock_id')
                    ->relationship('stock', 'serial_number')
                    ->searchable()
                    ->required(),

                self::userSearch(Select::make('user_id'), [Roles::CUSTOMER->value, Roles::DEALER->value])
                    ->label('Customer')
                    ->searchable()
                    ->required(),

                self::userSearch(Select::make('raised_by_id'), [Roles::DEALER->value, Roles::CUSTOMER->value, Roles::ADMIN->value, Roles::STAFF->value])
                    ->label('Raised By')
                    ->searchable()
                    ->required(),

                self::userSearch(Select::make('assignee_id'), [Roles::ADMIN->value, Roles::STAFF->value])
                    ->label('Assigned To')
                    ->searchable()
                    ->nullable(),

                Textarea::make('resolution')
                    ->nullable()
                    ->columnSpanFull(),

                DateTimePicker::make('resolved_at')
                    ->nullable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->searchable(),

                TextColumn::make('complaint')
                    ->limit(50)
                    ->searchable(),

                TextColumn::make('stock.serial_number')
                    ->label('Serial Number')
                    ->searchable(),

                TextColumn::make('user.name')
                    ->label('Customer')
                    ->searchable(),

                TextColumn::make('raisedBy.name')
                    ->label('Raised By')
                    ->searchable(),

                TextColumn::make('assignee.name')
                    ->label('Assigned To')
                    ->searchable()
                    ->placeholder('Unassigned'),

                TextColumn::make('status')
                    ->badge()
                    ->color(
                        fn(Complaint $record): string =>
                        $record->resolved_at ? 'success' : ($record->assignee_id ? 'warning' : 'danger')
                    )
                    ->getStateUsing(
                        fn(Complaint $record): string =>
                        $record->resolved_at ? 'Resolved' : ($record->assignee_id ? 'Assigned' : 'Open')
                    ),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'open' => 'Open',
                        'assigned' => 'Assigned',
                        'resolved' => 'Resolved',
                    ])
                    ->query(function (Builder $query, array $data) {
                        return match ($data['value']) {
                            'open' => $query->whereNull('assignee_id')->whereNull('resolved_at'),
                            'assigned' => $query->whereNotNull('assignee_id')->whereNull('resolved_at'),
                            'resolved' => $query->whereNotNull('resolved_at'),
                            default => $query,
                        };
                    }),

                Tables\Filters\SelectFilter::make('assignee')
                    ->relationship('assignee', 'name')
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),

                Action::make('assign')
                    ->visible(fn(Complaint $record) => $record->assignee_id === null && $record->resolved_at === null)
                    ->form([
                        self::userSearch(Select::make('assignee_id'), [Roles::ADMIN->value, Roles::STAFF->value])
                            ->label('Assign To')
                            ->searchable()
                            ->required(),
                    ])
                    ->action(function (Complaint $record, array $data): void {
                        // Update the assignee
                        $record->assignee_id = $data['assignee_id'];

                        // Add update to the history
                        $record->updates[] = new ComplaintUpdate(
                            type: ComplaintUpdateType::Assigned,
                            time: Carbon::now(),
                        );

                        $record->save();
                    }),

                Action::make('resolve')
                    ->visible(fn(Complaint $record) => $record->resolved_at === null)
                    ->form([
                        Textarea::make('resolution')
                            ->label('Resolution')
                            ->required(),
                    ])
                    ->action(function (Complaint $record, array $data): void {
                        // Update the resolution and resolved_at
                        $record->resolution = $data['resolution'];
                        $record->resolved_at = Carbon::now();

                        $record->save();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListComplaints::route('/'),
            'create' => Pages\CreateComplaint::route('/create'),
            'edit' => Pages\EditComplaint::route('/{record}/edit'),
        ];
    }
}
