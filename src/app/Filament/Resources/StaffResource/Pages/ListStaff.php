<?php

namespace App\Filament\Resources\StaffResource\Pages;

use App\Enums\Roles;
use App\Filament\Resources\StaffResource;
use App\Models\User;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class ListStaff extends ListRecords
{
    protected static string $resource = StaffResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->mutateFormDataUsing(function (array $data): array {
                    $data['password'] = Hash::make(Str::password());
                    return $data;
                })
                ->after(function (User $user) {
                    $user->assignRole(Roles::STAFF);
                }),
        ];
    }
}
