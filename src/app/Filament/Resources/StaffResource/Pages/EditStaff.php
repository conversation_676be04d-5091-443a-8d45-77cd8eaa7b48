<?php

namespace App\Filament\Resources\StaffResource\Pages;

use Action;
use App\Enums\Roles;
use App\Filament\Resources\StaffResource;
use App\Models\User;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\DB;

class EditStaff extends EditRecord
{
    protected static string $resource = StaffResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('Upgrade to Admin')
                ->link()
                ->requiresConfirmation(true)
                ->visible(fn(User $user) => !$user->hasRole(Roles::ADMIN) && !auth()->user()->is($user))
                ->action(function (User $user) {
                    DB::transaction(function () use ($user) {
                        $user->removeRole(Roles::STAFF);
                        $user->assignRole(Roles::ADMIN);
                    });
                }),
            Actions\Action::make('Downgrade to Staff')
                ->link()
                ->requiresConfirmation(true)
                ->visible(fn(User $user) => $user->hasRole(Roles::ADMIN) && !auth()->user()->is($user))
                ->action(function (Actions\Action  $action, User $user) {
                    // if only one admin, don't allow downgrade

                    if (User::isAdmin()->count() === 1) {
                        Notification::make()->title('Cannot downgrade only admin in the system.')->danger()->send();
                        return;
                    }

                    DB::transaction(function () use ($user) {
                        $user->removeRole(Roles::ADMIN);
                        $user->assignRole(Roles::STAFF);
                    });
                }),
        ];
    }
}
