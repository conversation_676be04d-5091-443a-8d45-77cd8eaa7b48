<?php

namespace App\Filament\Resources\StockResource\Pages;

use App\Filament\Resources\StockResource;
use App\Models\Stock;
use Closure;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Forms;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;

class ListStocks extends ListRecords
{
    protected static string $resource = StockResource::class;

    protected function generatePatternValues(string $pattern): false|array
    {
        if (!str_contains($pattern, '{') || !str_contains($pattern, '}')) {
            return false;
        }

        // find the sequence part, e.g. "202001{0001-9999}" or "{0001-9999}SMS"
        $sequencePart = substr($pattern, strpos($pattern, '{') + 1, strpos($pattern, '}') - strpos($pattern, '{') - 1);
        if (!$sequencePart) {
            return false;
        }

        $sequence = explode(',', $sequencePart);

        $patternValues = [];
        foreach ($sequence as $s) {
            $exploded = explode('-', $s);
            if (count($exploded) == 2) {
                for ($i = $exploded[0]; $i <= $exploded[1]; $i++) {
                    // re-pad $i with leading zeros upto the length of $exploded[0]

                    $replacer = (string)$i;
                    if (strlen($exploded[0]) > strlen($replacer)) {
                        $replacer = str_pad($replacer, strlen($exploded[0]), '0', STR_PAD_LEFT);
                    }

                    $serialNumber = str_replace('{' . $sequencePart . '}', $replacer, $pattern);
                    $patternValues[] = $serialNumber;
                }
            } else {
                $serialNumber = str_replace('{' . $sequencePart . '}', $s, $pattern);
                $patternValues[] = $serialNumber;
            }
        }

        return $patternValues;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('New By Pattern')
                ->label('New By Pattern')
                ->form([
                    Forms\Components\Select::make('product_id')->relationship('product', 'name')
                        ->required()
                        ->searchable()
                        ->preload(),
                    Forms\Components\TextInput::make('serial_number_pattern')->required()->maxLength(255)
                        //  e.g. "202001{0001-9999}" or "{0001-9999}SMS"
                        ->rules([
                            fn(): Closure => function (string $attribute, $value, Closure $fail) {
                                if (!preg_match('/\{((\d+-\d+)|(\d+))(,((\d+-\d+)|(\d+)))*}/', $value)) {
                                    $fail('The :attribute is invalid. A pattern must contain a sequence part. E.g. "202001{0001-9999}" or "{0001-9999}SMS"');
                                }
                            },
                            fn(): Closure => function (string $attribute, $value, Closure $fail) {
                                $patternValues = $this->generatePatternValues($value);

                                if ($patternValues && ($count = Stock::whereIn('serial_number', $patternValues)->count()) > 0) {
                                    $fail('One or more serial numbers that would be created already exist.');
                                }
                            }
                        ])
                        ->reactive()
                        ->helperText('Example: "202001{0001-9999}"')
                        ->afterStateUpdated(function (?string $state, Actions\Action $action, Forms\Components\TextInput $input) {
                            $func = function () use ($state) {
                                $patternValues = $this->generatePatternValues($state ?? '');
                                if (!$patternValues) {
                                    return new HtmlString('<p class="text-sm text-gray-600 dark:text-gray-400">Invalid Pattern. Example: "202001{0001-9999}"</p>');
                                }

                                $patternValues = implode(', ', $patternValues);
                                return new HtmlString(<<<HTML
                            <div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    Serial Number(s): $patternValues
                                </p>
                            </div>
                        HTML
                                );
                            };

                            $input->helperText($func);

                            $action->modalContentFooter($func);
                        }),
                ])
                ->action(function (array $data) {
                    $pattern = $data['serial_number_pattern'];
                    $product_id = $data['product_id'];

                    $patternValues = $this->generatePatternValues($pattern);
                    if (!$patternValues) {
                        Notification::make()->title('Invalid Pattern')->danger()->send();
                        return;
                    }

                    DB::transaction(function () use ($product_id, $patternValues) {
                        foreach ($patternValues as $pattern) {
                            Stock::create([
                                'product_id' => $product_id,
                                'serial_number' => $pattern
                            ]);
                        }
                    });

                    Notification::make()->title('New Stock(s) Created')->body(
                        'Serial Number(s): ' . implode(', ', $patternValues)
                    )->success()->send();
                })
                ->requiresConfirmation(true)
        ];
    }
}
