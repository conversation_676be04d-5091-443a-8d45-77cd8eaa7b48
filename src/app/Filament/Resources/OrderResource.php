<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrderResource\Pages;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Stock;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('user_id')->relationship('user', 'name', fn(Builder $query) => $query->isDealer())
                    ->label('Dealer')
                    ->searchable()
                    ->helperText('The dealer who purchased this order.'),
                DatePicker::make('order_placed_at')->required()
                    ->helperText('When was this order placed? Used to resolve warranty.'),
                TextInput::make('pincode')->required(),
                TextInput::make('address')->required(),
                TextInput::make('phone_number')->tel(),
                TextInput::make('email')->email(),
                TextInput::make('bill_number')->required(),

                Repeater::make('orderItems')->relationship('orderItems')->schema([
                    Select::make('product_id')->relationship('product', 'name')->required(),
                    TextInput::make('quantity')->required()->numeric()->minValue(1),
                    Select::make('stock_id')
                        ->multiple()
                        ->helperText('Select a product and then select some of its stock.')
                        ->options(function (OrderItem $item, Get $get, Select $select) {
                            //                            ->relationship('stocks', 'serial_number',
                            //                                modifyQueryUsing: function (Builder $query, Get $get) {
                            //                                    if (!($productId = $get('product_id'))) {
                            //                                        return $query->whereRaw('1 = 0');
                            //                                    }
                            //                                    return $query->where('product_id', $productId)->whereNull('order_item_id');
                            //                                }
                            //                            )
                            Log::info('Searching : ' . $select->getSearchingMessage());
                            return Stock::whereNull('order_item_id')
                                ->where('product_id', $get('product_id'))
                                ->when($select->getSearchingMessage(),
                                    fn(Builder $query) => $query->where('serial_number', 'like', "%{$select->getSearchingMessage()}%")
                                )
                                ->orderBy('serial_number')
                                ->pluck('serial_number', 'serial_number');
                        })
                        ->searchable()
                        ->loadStateFromRelationshipsUsing(function (Select $component, OrderItem $orderItem, $state) {
                            if (filled($state)) {
                                return;
                            }

                            $component->state(
                                $orderItem->stocks()->orderBy('serial_number')->pluck('serial_number')->all()
                            );
                        })
                        ->saveRelationshipsUsing(function (OrderItem $orderItem, ?array $state) {
                            if ($state === null) { // Because filament adds nullable in validation by default
                                return;
                            }

                            Stock::where('order_item_id', $orderItem->id)->update([
                                'order_item_id' => null
                            ]);
                            Stock::whereIn('serial_number', $state)->update([
                                'order_item_id' => $orderItem->id
                            ]);
                        }),
                ])->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->searchable(),
                Tables\Columns\TextColumn::make('bill_number')->searchable(),
                Tables\Columns\TextColumn::make('order_placed_at')->sortable(),
                Tables\Columns\TextColumn::make('pincode')->sortable(),
                Tables\Columns\TextColumn::make('address')->searchable(),
                Tables\Columns\TextColumn::make('phone_number')->searchable(),
                Tables\Columns\TextColumn::make('user.dealership_code')->label('Dealer Name')->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            //            'create' => Pages\CreateOrder::route('/create'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
        ];
    }
}
