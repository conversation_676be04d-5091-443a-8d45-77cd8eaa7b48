<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StockResource\Pages;
use App\Models\Stock;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class StockResource extends Resource
{
    protected static ?string $model = Stock::class;

    protected static ?string $navigationIcon = 'heroicon-o-archive-box';

    protected static ?string $recordTitleAttribute = 'serial_number';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_id')->relationship('product', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->disabledOn('edit'),
                Forms\Components\TextInput::make('serial_number')
                    ->required()
                    ->maxLength(255),
                Forms\Components\DatePicker::make('warranty_start')->nullable(),
                Forms\Components\DatePicker::make('warranty_end')->nullable()->suffixAction(
                    Forms\Components\Actions\Action::make('autoCalculate')
                        ->icon('heroicon-o-calculator')
                        ->action(function (Forms\Get $get, Forms\Set $set) {
                            $date = $get('warranty_start');
                            if (!$date) {
                                Notification::make()->title('Failed to calculate end date')->body('Please select a start date first.')
                                    ->danger()
                                    ->send();
                                return;
                            }
                            $duration = $get('warranty.duration');
                            if (!is_numeric($duration)) {
                                Notification::make()->title('Failed to calculate end date')
                                    ->body('Please select a warranty duration first.')->danger()->send();
                                return;
                            }
                            $set('warranty_end', Carbon::make($date)->addMonths((int)$duration)->toDateString());
                            Notification::make()->title('Calculated end date')->success()->send();
                        })
                ),
                Forms\Components\TextInput::make('dealer_bill_number')->nullable()
                    ->helperText('This will be filled by the dealer upon sale of this stock.'),
                Forms\Components\Select::make('warrantee_id')
                    ->relationship('warrantee', 'phone_number')
                    ->searchable()
                    ->nullable(),
                Forms\Components\Fieldset::make('Warranty Details')->schema(ProductResource::warrantySpecSchema())->disabledOn('edit')
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('serial_number')->searchable(),
                Tables\Columns\TextColumn::make('warranty_start')->sortable(),
                Tables\Columns\TextColumn::make('warranty_end')->sortable(),
                Tables\Columns\TextColumn::make('dealer_bill_number')->searchable(),
                Tables\Columns\TextColumn::make('warrantee.phone_number')->searchable(),
                Tables\Columns\TextColumn::make('product.name')->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStocks::route('/'),
            //            'create' => Pages\CreateStock::route('/create'),
            'edit' => Pages\EditStock::route('/{record}/edit'),
        ];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            'Product' => $record->product->name,
        ];
    }

    public static function modifyGlobalSearchQuery(Builder $query, string $search): void
    {
        $query->with(['product']);
    }
}
