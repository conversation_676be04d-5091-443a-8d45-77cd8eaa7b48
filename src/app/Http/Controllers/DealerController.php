<?php

namespace App\Http\Controllers;

use App\Http\Resources\UserResource;
use Illuminate\Http\Request;

class DealerController extends Controller
{
    public function activate(Request $request)
    {
        $request->validate([
            'dealership_activation_code' => 'required|string|max:255',
        ]);

        abort_if($request->user()->dealership_activated_at, 400, 'Dealership already activated');

        if ($request->user()->dealership_activation_code === $request->dealership_activation_code) {
            $request->user()->update([
                'dealership_activated_at' => now(),
            ]);

            return response()->json(new UserResource($request->user()));
        }

        return response()->json(['message' => 'Invalid activation code'], 400);
    }
}
