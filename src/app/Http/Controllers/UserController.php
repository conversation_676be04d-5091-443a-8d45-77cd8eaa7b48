<?php

namespace App\Http\Controllers;

use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\UpdateUserNameRequest;

class UserController extends Controller
{
    public function show(User $user)
    {
        abort_if(!auth()->user(), 401, 'Unauthorized');
        abort_if(auth()->user()->isNot($user), 403, 'Forbidden');

        return response()->json(new UserResource(auth()->user()));
    }

    /**
     * Update the authenticated user's name
     *
     * @param UpdateUserNameRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateName(UpdateUserNameRequest $request)
    {
        $user = Auth::user();
        
        $user->update([
            'name' => $request->name
        ]);

        return response()->json([
            'message' => 'Name updated successfully',
            'user' => new UserResource($user)
        ]);
    }
}
