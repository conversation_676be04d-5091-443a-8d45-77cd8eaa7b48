<?php

namespace App\Http\Controllers;

use App\Http\Resources\StockResource;
use App\Models\Stock;
use Illuminate\Support\Facades\App;

class CustomerStockController extends Controller
{
    public function index()
    {
        $registrations = Stock::with('product')->where('warrantee_id', auth()->id())->get();
        return StockResource::collection($registrations);
    }

    public function show($stock)
    {
        $stock = Stock::with('product')->where('warrantee_id', auth()->id())->find($stock);
        if (!$stock) {
            return response()->json(['message' => 'Warranty Registration not found'], 404);
        }
        return new StockResource($stock);
    }

    public function find(string $serial)
    {
        if ($serial === 'SNVAL' && App::isLocal()) {
            $stock = Stock::whereNull('warrantee_id')->with('product')->inRandomOrder()->firstOrFail();
        } elseif ($serial === 'SNINV' && App::isLocal()) {
            $stock = Stock::whereNotNull('warrantee_id')->whereNot('warrantee_id', auth()->id())->with('product')->inRandomOrder()
                ->firstOrFail();
        } else {
            $stock = Stock::where('serial_number', $serial)->with('product')->firstOrFail();
        }

        if ($stock->warrantee_id !== auth()->id()) {
            $stock->unsetRelation('orderItems');
        }

        return new StockResource($stock);
    }

    public function redeem(string $serial)
    {
        $stock = Stock::where('serial_number', $serial)->firstOrFail();

        $stock->register(auth()->user());
        $stock->load('product');

        return new StockResource($stock);
    }
}
