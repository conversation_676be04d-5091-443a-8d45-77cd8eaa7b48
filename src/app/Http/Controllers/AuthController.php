<?php

namespace App\Http\Controllers;

use App\Enums\Roles;
use App\Http\Resources\UserResource;
use App\Services\AcceptanceService;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Services\OtpService;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    protected $otpService;

    public function __construct(OtpService $otpService)
    {
        $this->otpService = $otpService;
    }

    public function signup(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone_number' => 'required|string|max:15|unique:users',
            'password' => 'required_if:user_type,staff,dealer|string|min:8',
            'user_type' => 'required|string|in:customer,staff,dealer',
            'otp' => 'required_if:user_type,customer|string',
        ]);

        if ($request->user_type === 'customer') {
            if (!$this->otpService->consumeOtp($request->phone_number, $request->otp)) {
                return response()->json(['message' => 'Invalid OTP'], 401);
            }

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'password' => Hash::make('default_password'), // Set a default password
                'user_type' => $request->user_type,
            ]);
        } else {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'password' => Hash::make($request->password),
                'user_type' => $request->user_type,
            ]);
        }

        return response()->json(['message' => 'User created successfully'], 201);
    }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json(['message' => 'Invalid credentials'], 401);
        }

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json(['access_token' => $token, 'token_type' => 'Bearer'], 200);
    }

    public function loginWithOtp(Request $request, AcceptanceService $acceptanceService)
    {
        $request->validate([
            'phone_number' => 'required|string|max:15',
            'otp' => 'required|string',
            'app_type' => 'required|string|in:family,partner',
        ]);

        $role = match ($request->app_type) {
            'family' => Roles::CUSTOMER,
            'partner' => Roles::DEALER,
            default => abort(400, 'Invalid app type'),
        };

        if (!$this->otpService->consumeOtp($request->phone_number, $request->otp)) {
            return response()->json(['message' => 'Invalid OTP'], 401);
        }

        $user = User::where('phone_number', $request->phone_number)->first();

        if (!$user) {
            $user = DB::transaction(function () use ($request, $role) {
                $user = User::create([
                    'name' => null,
                    'email' => null,
                    'phone_number' => $request->phone_number,
                    'password' => Hash::make(Str::password(14)),
                ]);
                $user->assignRole($role);
                return $user;
            });
        }

        if (!$user) {
            return response()->json(['message' => 'Invalid OTP or phone number'], 401);
        }

        if (!$user->hasRole($role)) {
            return response()->json([
                'message' => match (true) {
                    $user->hasRole(Roles::CUSTOMER) => 'Please login from the Ralicoan Family app.',
                    $user->hasRole(Roles::DEALER) => 'Please login from the Ralicoan Partner app.',
                    $user->hasRole([Roles::STAFF, Roles::ADMIN]) => 'Please login from the admin dashboard.',
                    default => 'Invalid OTP or phone number',
                }
            ], 401);
        }

        auth()->login($user);

        $acceptanceService->claimAcceptances($request, $user);

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json(['access_token' => $token, 'token_type' => 'Bearer', 'user' => new UserResource($user)], 200);
    }

    public function logout()
    {
        auth()->user()->currentAccessToken()->delete();

        return response()->json(['message' => 'Logged out successfully'], 200);
    }

    public function sendOtp(Request $request, AcceptanceService $acceptanceService)
    {
        $request->validate([
            'phone_number' => 'required|string|max:15',
        ]);

        $acceptanceService->checkOrCreateAcceptance($request, $request->phone_number);

        $this->otpService->generateAndSendOtp($request->phone_number);

        return response()->json(['message' => 'OTP sent successfully'], 200);
    }
}
