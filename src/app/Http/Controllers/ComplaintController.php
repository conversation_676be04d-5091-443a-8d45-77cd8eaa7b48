<?php

namespace App\Http\Controllers;

use App\Data\ComplaintUpdate;
use App\Enums\ComplaintUpdateType;
use App\Enums\Roles;
use App\Http\Requests\AssignComplaintRequest;
use App\Http\Requests\StoreComplaintRequest;
use App\Http\Requests\UpdateComplaintRequest;
use App\Http\Resources\ComplaintResource;
use App\Models\Complaint;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ComplaintController extends Controller
{
    /**
     * Display a listing of the complaints.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $user = $request->user();
        $query = Complaint::query()
            ->when($user->hasRole(Roles::STAFF), fn($q) => $q->where('assignee_id', $user->id))
            ->when($user->hasRole(Roles::DEALER) || $user->hasRole(Roles::CUSTOMER), fn($q) => $q->where('user_id', $user->id));

        // Apply optional filters
        if ($request->has('resolved')) {
            if ($request->boolean('resolved')) {
                $query->whereNotNull('resolved_at');
            } else {
                $query->whereNull('resolved_at');
            }
        }

        if ($request->has('stock_id')) {
            $query->where('stock_id', $request->input('stock_id'));
        }

        // Load relationships
        $query->with(['stock.product', 'user', 'assignee', 'raisedBy']);

        // Paginate results
        $complaints = $query->latest()->paginate(10);

        return ComplaintResource::collection($complaints);
    }

    /**
     * Store a newly created complaint in storage.
     */
    public function store(StoreComplaintRequest $request): ComplaintResource
    {
        $complaint = Complaint::create($request->validated());

        // Load relationships for the response
        $complaint->load(['stock.product', 'user', 'assignee', 'raisedBy']);

        return new ComplaintResource($complaint);
    }

    /**
     * Display the specified complaint.
     */
    public function show(Complaint $complaint): ComplaintResource
    {
        $this->authorize('view', $complaint);

        // Load relationships
        $complaint->load(['stock.product', 'user', 'assignee', 'raisedBy']);

        return new ComplaintResource($complaint);
    }

    /**
     * Update the specified complaint in storage.
     */
    public function update(UpdateComplaintRequest $request, Complaint $complaint): ComplaintResource
    {
        $complaint->update($request->validated());

        // Load relationships for the response
        $complaint->load(['stock.product', 'user', 'assignee', 'raisedBy']);

        return new ComplaintResource($complaint);
    }

    /**
     * Remove the specified complaint from storage.
     */
    public function destroy(Complaint $complaint): Response
    {
        $this->authorize('delete', $complaint);

        $complaint->delete();

        return response()->noContent();
    }

    /**
     * Assign a staff member to a complaint.
     */
    public function assign(AssignComplaintRequest $request, Complaint $complaint): ComplaintResource
    {
        $complaint->assignee_id = $request->input('assignee_id');

        $complaint->save();

        // Load relationships for the response
        $complaint->load(['stock.product', 'user', 'assignee', 'raisedBy']);

        return new ComplaintResource($complaint);
    }
}
