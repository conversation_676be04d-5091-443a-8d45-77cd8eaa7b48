<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateComplaintRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update', $this->route('complaint'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'complaint' => ['sometimes', 'string'],
            'resolution' => ['sometimes', 'string'],
            'resolved_at' => ['sometimes', 'nullable', 'date'],
            'assignee_id' => ['sometimes', 'nullable', 'exists:users,id'],
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // If updating with a resolution and resolved_at is not provided, set it to now
        if ($this->has('resolution') && $this->filled('resolution') && !$this->has('resolved_at')) {
            $this->merge([
                'resolved_at' => now(),
            ]);
        }
    }
}
