<?php

namespace App\Http\Requests;

use App\Enums\Roles;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class AssignComplaintRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('assign', $this->route('complaint'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'assignee_id' => ['required', 'exists:users,id'],
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param Validator $validator
     * @return void
     */
    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            $assigneeId = $this->input('assignee_id');

            // Check if the assignee is a staff member
            $assignee = \App\Models\User::find($assigneeId);
            if (!$assignee || !$assignee->hasRole(Roles::STAFF)) {
                $validator->errors()->add('assignee_id', 'The assignee must be a staff member.');
            }
        });
    }
}
