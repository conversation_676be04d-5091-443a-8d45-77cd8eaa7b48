<?php

namespace App\Http\Requests;

use App\Enums\Roles;
use App\Models\Stock;
use App\Models\Complaint;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreComplaintRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', \App\Models\Complaint::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'complaint' => ['required', 'string'],
            'stock_id' => [
                'required',
                'exists:stocks,id',
                function ($attribute, $value, $fail) {
                    // Check if the stock belongs to the user
                    $stock = Stock::find($value);

                    // If the user is a customer, ensure they own the stock
                    if (($this->user()->hasRole(Roles::CUSTOMER) || $this->user()->hasRole(Roles::DEALER)) && $stock && $stock->warrantee_id !== $this->user()->id) {
                        $fail('You can only submit complaints for products registered under your warranty.');
                    }

                    // Check if there's an existing unresolved complaint for this stock and user
                    $existingComplaint = Complaint::where('stock_id', $value)
                        ->where('user_id', $this->user_id)
                        ->whereNull('resolved_at')
                        ->exists();

                    if ($existingComplaint) {
                        $fail('There is already an unresolved complaint for this product. Please wait for the existing complaint to be resolved.');
                    }
                }
            ],
            'raised_by_id' => ['sometimes', 'exists:users,id'],
            'user_id' => ['required', 'exists:users,id'],
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set the user_id to the authenticated user if not provided
        $this->merge([
            'user_id' => $this->user()->id,
            // If raised_by_id is not provided, set it to the authenticated user
            'raised_by_id' => $this->raised_by_id ?? $this->user()->id,
        ]);
    }
}
