<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property-read User $resource
 */
class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            ...$this->resource->only([
                'id',
                'name',
                'email',
                'phone_number',
                'email_verified_at',
                'pincode',
                'address',
                'dealership_code',
                'dealership_activated_at',
                'meta',
                'created_at',
                'updated_at',
                'deleted_at',
            ])
        ];
    }
}
