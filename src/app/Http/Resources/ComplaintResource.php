<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Complaint
 */
class ComplaintResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'complaint' => $this->complaint,
            'resolution' => $this->resolution,
            'resolved_at' => $this->resolved_at,
            'updates' => $this->updates,
            'stock' => $this->whenLoaded('stock', function () {
                return new StockResource($this->stock);
            }),
            'user' => $this->whenLoaded('user', function () {
                return new UserResource($this->user);
            }),
            'assignee' => $this->whenLoaded('assignee', function () {
                return $this->assignee ? new UserMinimalResource($this->assignee) : null;
            }),
            'raised_by' => $this->whenLoaded('raisedBy', function () {
                return $this->raisedBy ? new UserMinimalResource($this->raisedBy) : null;
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
