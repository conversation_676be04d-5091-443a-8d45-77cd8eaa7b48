<?php

namespace App\Models;

use App\Data\ComplaintUpdate;
use App\Observers\ComplaintObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;
use Spatie\LaravelData\DataCollection;

/**
 * @property DataCollection<ComplaintUpdate> $updates
 */
#[ObservedBy(ComplaintObserver::class)]
class Complaint extends Model implements \OwenIt\Auditing\Contracts\Auditable
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'complaint',
        'resolution',
        'resolved_at',
        'updates',
        'stock_id',
        'user_id',
        'assignee_id',
        'raised_by_id',
    ];

    protected $casts = [
        'updates' => DataCollection::class . ':' . ComplaintUpdate::class . ',default',
        'resolved_at' => 'datetime',
    ];

    // Relationships

    public function stock(): BelongsTo
    {
        return $this->belongsTo(Stock::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assignee_id');
    }

    public function raisedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'raised_by_id');
    }
}
