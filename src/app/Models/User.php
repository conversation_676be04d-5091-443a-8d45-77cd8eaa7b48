<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\Roles;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Ladder\HasRoles;
use Laravel\Sanctum\HasApiTokens;
use OwenIt\Auditing\Auditable;

class User extends Authenticatable implements \OwenIt\Auditing\Contracts\Auditable, FilamentUser, \Illuminate\Contracts\Auth\Authenticatable
{
    use HasFactory, Notifiable, HasApiTokens, HasRoles, SoftDeletes, Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone_number',
        'password',
        'pincode',
        'address',
        'email_verified_at',
        'dealership_code',
        'dealership_activation_code',
        'dealership_activated_at',
        'meta',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $auditExclude = [
        'password',
        'remember_token',
    ];

    public function canAccessPanel(Panel $panel): bool
    {
        return $this->hasRole(Roles::ADMIN) || $this->hasRole(Roles::STAFF);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'meta' => 'array',
            'dealership_activated_at' => 'datetime',
        ];
    }

    public function warrantedStocks(): HasMany
    {
        return $this->hasMany(Stock::class, 'warrantee_id');
    }

    public function scopeIsCustomer(Builder $query): Builder
    {
        return $query->whereRelation('roles', 'role', Roles::CUSTOMER->value);
    }

    public function scopeIsDealer(Builder $query): Builder
    {
        return $query->whereRelation('roles', 'role', Roles::DEALER->value);
    }

    public function scopeIsStaff(Builder $query): Builder
    {
        return $query->whereRelation('roles', 'role', Roles::STAFF->value);
    }

    public function scopeIsAdmin(Builder $query): Builder
    {
        return $query->whereRelation('roles', 'role', Roles::ADMIN->value);
    }

    public function scopeIsAnyRole(Builder $query, array $roles): Builder
    {
        return $query->whereRelation('roles', fn($query) => $query->whereIn('role', $roles));
    }

    public function assignRole(Roles $role)
    {
        return $this->roles()->updateOrCreate([
            'role' => $role->value
        ], [
            'role' => $role->value
        ]);
    }

    public function removeRole(Roles $role)
    {
        return $this->roles()->where('role', $role->value)->delete();
    }
}
