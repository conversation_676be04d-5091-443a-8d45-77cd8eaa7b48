<?php

namespace App\Models;

use App\Data\WarrantySpec;
use App\Enums\ProductStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;

class Product extends Model implements \OwenIt\Auditing\Contracts\Auditable
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'name',
        'price',
        'description',
        'specifications',
        'warranty',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'specifications' => 'array',
            'warranty' => WarrantySpec::class,
            'price' => 'decimal:2',
            'status' => ProductStatus::class,
        ];
    }

    public function stocks(): HasMany
    {
        return $this->hasMany(Stock::class, 'product_id');
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'product_id');
    }
}
