<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Auditable;

class OrderItem extends Model implements \OwenIt\Auditing\Contracts\Auditable
{
    use HasFactory, Auditable;

    protected $fillable = [
        'order_id',
        'product_id',
        'quantity',
        'meta'
    ];

    protected function casts(): array
    {
        return [
            'meta' => 'array',
        ];
    }

    // <editor-fold desc="Relations">

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class)->withTrashed();
    }

    public function stocks(): HasMany
    {
        return $this->hasMany(Stock::class);
    }

    // </editor-fold>
}
