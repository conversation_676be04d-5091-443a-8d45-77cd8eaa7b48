<?php

namespace App\Models;

use App\Actions\Sms\SendWarrantyConfirmationSms;
use App\Data\WarrantySpec;
use App\Observers\StockObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;

#[ObservedBy(StockObserver::class)]
class Stock extends Model implements \OwenIt\Auditing\Contracts\Auditable
{
    use SoftDeletes, Auditable, HasFactory;

    protected $fillable = [
        'serial_number',
        'warranty_start',
        'warranty_end',
        'warrantee_id',
        'warranty',
        'product_id',
        'dealer_bill_number',
    ];

    protected function casts(): array
    {
        return [
            'warranty_end' => 'datetime',
            'warranty_start' => 'datetime',
            'warranty' => WarrantySpec::class,
        ];
    }

    // <editor-fold desc="Relationships">

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function warrantee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'warrantee_id');
    }

    public function orderItem(): BelongsTo
    {
        return $this->belongsTo(OrderItem::class, 'order_item_id');
    }

    // </editor-fold>

    public function register(User $user): bool
    {
        abort_if($this->warrantee_id, 400, 'Warranty already registered');

        //        abort_unless($this->warrantee->isCustomer(), 400, 'Warranty can only be registered to a customer');

        $spec = $this->warranty ?? $this->product->warranty;
        abort_if($spec === null, 400, 'Warranty not applicable for this product');

        $this->warrantee_id = $user->id;

        // calculate sale date, and provide leeway for dealer sale
        $saleDate = $this->created_at;

        if ($this->orderItem?->order?->order_placed_at) {
            $saleDate = $this->orderItem->order->order_placed_at;
        }

        $this->warranty_start = min($saleDate->clone()->addMonths($spec->leeway), now());
        $this->warranty_end = $this->warranty_start->clone()->addMonths($spec->duration);

        return $this->saveOrFail();
    }
}
