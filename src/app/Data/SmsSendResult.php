<?php

namespace App\Data;

use Illuminate\Http\Client\Response;

readonly class SmsSendResult
{
    public function __construct(
        public bool $success,
        /**
         * @var Response|array
         */
        public mixed $response,

    )
    {
    }

    public function toArray(): array
    {
        return [
            'success' => true,
            'response' => $this->response instanceof Response ? $this->response->json() : (is_array($this->response) || is_string($this->response) ? $this->response : null),
        ];
    }
}
