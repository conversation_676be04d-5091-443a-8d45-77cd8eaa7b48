<?php

namespace App\Data;

use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class Agreeable extends Data
{
    public string $id;
    public string $label;
    public Carbon $createdAt;
    public string $content;

    protected function filePath()
    {
        return 'agreeables/' . $this->id . '/' . $this->createdAt->unix() . '.json';
    }

    public function save(): bool
    {
        $disk = Storage::disk('local');

        return $disk->put($this->filePath(), $this->toJson(JSON_PRETTY_PRINT));
    }

    public static function latest(string $id, string $label): Agreeable
    {
        $disk = Storage::disk('local');
        $item = collect($disk->files('agreeables/' . $id))
            ->sort(function ($a, $b) {
                // Sort by date encoded as filename unix
                $nameA = pathinfo($a)['filename'];
                $nameB = pathinfo($b)['filename'];
                $nameA = str_ends_with($nameA, '.json') ? substr($nameA, 0, -4) : $nameA;
                $nameB = str_ends_with($nameB, '.json') ? substr($nameB, 0, -4) : $nameB;
                return (int)$nameB <=> (int)$nameA;
            })
            ->first();

        return $item ? static::from(json_decode($disk->get($item), true)) : Agreeable::from([
            'id' => $id,
            'createdAt' => now(),
            'content' => '',
            'label' => $label
        ]);
    }
}
