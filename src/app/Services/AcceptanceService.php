<?php

namespace App\Services;

use App\Data\Agreeable;
use App\Models\Acceptance;
use App\Models\User;
use Illuminate\Http\Request;

class AcceptanceService
{
    public function checkOrCreateAcceptance(Request $request, string $guestId)
    {
        $required = ['terms_and_conditions', 'privacy_policy'];
        $acceptances = Acceptance::query()
            ->where('guest_id', $guestId)
            ->where('ip_address', $request->ip())
            ->where('created_at', '>', now()->subHour())
            ->latest()
            ->get()
            ->unique('document');

        foreach ([...$acceptances->all()] as $acceptance) {
            assert($acceptance instanceof Acceptance);
            $agreeable = Agreeable::latest($acceptance->document, $acceptance->document);

            if ($agreeable->createdAt->unix() <= (int)$acceptance->document_version) {
                $required = array_diff($required, [$acceptance->document]);
            } else {
                $acceptances = $acceptances->where('document', '!=', $acceptance->document);
            }
        }

        if (empty($required)) {
            return $acceptances;
        }

        $request->validate(collect($required)->mapWithKeys(fn($document) => [$document => 'required|accepted'])->toArray());

        foreach ($required as $document) {
            $acceptance = Acceptance::create([
                'document' => $document,
                'document_version' => Agreeable::latest($document, $document)->createdAt->unix(),
                'user_agent' => $request->userAgent(),
                'ip_address' => $request->ip(),
                'guest_id' => $guestId,
            ]);
            $acceptances->push($acceptance);
        }

        return [$acceptances];
    }

    public function claimAcceptances(Request $request, User $user): void
    {
        $acceptances = Acceptance::query()
            ->where('guest_id', $user->phone_number)
            ->where('ip_address', $request->ip())
            ->where('created_at', '>', now()->subHour())
            ->whereNull('user_id')
            ->get();

        foreach($acceptances as $acceptance) {
            $acceptance->update([
                'user_id' => $user->id
            ]);
        }
    }
}
