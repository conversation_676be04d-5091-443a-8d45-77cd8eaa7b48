<?php

namespace App\Services;

use App\Data\SmsSendResult;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class SmsService
{
    const PRV_POWERSTEXT = 'POWERSTEXT';
    const PRV_SMSSWEET = 'SMSSWEET';
    protected $apiUrl;
    protected $apiUsername;
    protected $apiPassword;
    protected $apiToken;
    protected $apiProvider;


    /** Public methods */
    public function __construct(protected KafkaProducerService $kafkaProducer)
    {
        if (config('sms.provider') === self::PRV_POWERSTEXT) {
            $this->apiProvider = self::PRV_POWERSTEXT;
            $this->apiUrl = config('sms.api_url');
            $this->apiToken = config('sms.token_key');
        }
        if (config('sms.provider') === self::PRV_SMSSWEET) {
            $this->apiProvider = self::PRV_POWERSTEXT;
            $this->apiUrl = config('sms.api_url');
            $this->apiUsername = config('sms.username');
            $this->apiPassword = config('sms.password');
        }

        if ($this->apiUrl === null) {
            throw new \Exception('SMS provider not set');
        }
    }

    public function sendSms($phoneNumber, $message, $templateId): SmsSendResult
    {
        if ($this->apiProvider === self::PRV_POWERSTEXT) {
            return $this->sendSmsPowersText($phoneNumber, $message, $templateId);
        }
        return $this->sendSmsSmsSweet($phoneNumber, $message, $templateId);
    }

    public function verifySmsDelivery($messageId): SmsSendResult
    {

        if ($this->apiProvider === self::PRV_POWERSTEXT) {
            return $this->verifySmsDeliveryPowersText($messageId);
        }
        return $this->verifySmsDeliverySmsSweet($messageId);

    }

    /** Private methods */

    private function sendSmsPowersText($phoneNumber, $message, $templateId): SmsSendResult
    {
        try {
            /** TODO: Check if POST method is supported, else use GET method */
            $response = Http::withHeaders($this->spoofHeaders())
                ->post($this->apiUrl . '?' . http_build_query([
                        'authentic-key' => $this->apiToken,
                        'senderid' => config('sms.sender_id'),
                        'templateid' => $templateId,
                        'route' => config('sms.route'),
                        'number' => $phoneNumber,
                        'message' => $message,
                    ]));
        } catch (Exception $e) {
            Log::error('Error sending SMS via PowerText: ' . $e->getMessage());
            return new SmsSendResult(false, $e->getMessage());
        }

        $this->kafkaProducer->log('sms_logs', "Sent SMS via PowerText to $phoneNumber with message: '$message'", [
            'request' => [
                'url' => $this->apiUrl,
                'numbers' => $phoneNumber,
                'message' => $message,
                'body' => [
                    'authentic-key' => $this->apiToken,
                    'senderid' => config('sms.sender_id'),
                    'templateid' => $templateId,
                    'route' => config('sms.route'),
                    'number' => $phoneNumber,
                    'message' => $message,
                ],
                'headers' => $this->spoofHeaders(),
                'effective_uri' => (string)$response->effectiveUri(),
            ],
            'response' => [
                'body' => $response->json(),
                'status' => $response->status(),
            ],
        ]);
        return new SmsSendResult(strtolower($response->json('status')) === 'success', $response->json());
    }

    private function sendSmsSmsSweet($phoneNumber, $message, $templateId): SmsSendResult
    {
        try {
            $response = Http::withHeaders($this->spoofHeaders())->get($this->apiUrl, [
                'username' => $this->apiUsername,
                'pass' => $this->apiPassword,
                'route' => config('sms.route'),
                'senderid' => config('sms.sender_id'),
                'numbers' => $phoneNumber,
                'message' => $message, // Message must match the template
                'templateid' => $templateId,
            ]);
        } catch (Exception $e) {
            Log::error('Error sending SMS via SmsSweet: ' . $e->getMessage());
            return new SmsSendResult(false, $e->getMessage());
        }

        $responseData = $response->json();
        // todo -- check
        $status = ($responseData['Status'] ?? null) == 'Success';

        $this->kafkaProducer->log('sms_logs', "Sent SMS via SmsSweet to $phoneNumber with message: $message", [
            'request' => [
                'url' => $this->apiUrl,
                'numbers' => $phoneNumber,
                'message' => $message,
                'templateid' => $templateId
            ],
            'response' => [
                'body' => $response->json(),
                'status' => $response->status(),
            ]
        ]);

        return new SmsSendResult($status == 1, $response->json());
    }


    /** SMS Delivery Methods */
    private function verifySmsDeliveryPowersText($messageId): SmsSendResult
    {
        try {
            $response = Http::withHeaders($this->spoofHeaders())->get($this->apiUrl, [
                'authentic-key' => $this->apiToken,
                'messageid' => $messageId,
            ]);
        } catch (Exception $e) {
            Log::error('Error verifying SMS delivery via PowerText: ' . $e->getMessage());
            return new SmsSendResult(false, $e->getMessage());
        }

        $this->kafkaProducer->log('sms_logs', "Verified SMS delivery for message ID: $messageId", [
            'response' => [
                'body' => $response->json(),
                'status' => $response->status(),
            ]
        ]);
        return new SmsSendResult(strtolower($response->json('status')) === 'success', $response->json());
    }

    private function spoofHeaders(): array
    {
        // Spoof headers
        if (config('sms.spoof_headers')) {
            return [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
                'Accept' => 'application/json',
                'Content-Type' => 'application/x-www-form-urlencoded',
            ];
        }
        return [];
    }
}
