<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Kafka\Facades\Kafka;

class KafkaProducerService
{
    public const SECRETS = ['authentic-key'];

    /**
     * @param string $topic The topic to be published.
     * @param string $message The message to be sent.
     * @param array $context The context of the log message.
     * @param array $kafkaMessageBody Additional data to be sent with the message.
     * @return bool
     */
    public function log(string $topic, string $message, array $context = [], array $kafkaMessageBody = []): bool
    {
        try {
            $this->hideSecrets($context);
            return Kafka::publish()->onTopic($topic)->withBody([
                'message' => $message,
                'context' => $context,
                ...$kafkaMessageBody
            ])->send();
        } catch (\Throwable $e) {
            // use laravel logger as fallback

            report($e);

            Log::info($message, [
                'context' => $context,
                'kafkaMessageBody' => $kafkaMessageBody
            ]);

            return false;
        }
    }

    public function audit(string $topic, array $body): bool
    {
        try {
            return Kafka::publish()->onTopic($topic)->withBody($body)->send();
        } catch (\Throwable $e) {
            // use laravel logger as fallback
            report($e);

            Log::info('Kafka Audit Ingest Failed', ['topic' => $topic, 'body' => $body]);

            //            $cachedAudits = Cache::get('audits');
            //            $cachedAudits = is_array($cachedAudits) ? $cachedAudits : [];
            //            $cachedAudits[] = [
            //                'topic' => $topic,
            //                'body' => $body
            //            ];
            //
            //            if (count($cachedAudits) > 10000) {
            //                $cachedAudits = array_slice($cachedAudits, -10000);
            //            }
            //
            //            Cache::set('audits', $cachedAudits);

            return false;
        }
    }

    protected function hideSecrets(array &$data): int
    {
        $replaced = 0;
        foreach (self::SECRETS as $secret) {
            // recursively hide secrets in data. look for it in two ways:
            // either as array key/value pair where key is the secret
            // or in all array values where the value is a string and contains the secret key as uri parameter, e.g. ?authentic-key=...
            foreach (array_keys($data) as $key) {
                if ($key === $secret) {
                    $data[$key] = $this->replaceSecretValue($data[$key]);
                    $replaced++;
                }
                if (is_string($data[$key]) && str_contains($data[$key], $secret)) {
                    $data[$key] = preg_replace("/{$secret}=[^&]*&?/", $secret . '=' . $this->replaceSecretValue($data[$key]) . '&',
                        $data[$key]);
                }
                if (is_array($data[$key])) {
                    $replaced += $this->hideSecrets($data[$key]);
                }
            }

        }
        return $replaced;
    }

    protected function replaceSecretValue(mixed $value): ?string
    {
        if (is_null($value)) {
            return null;
        }
        if (is_string($value)) {
            return 'redacted(hash=' . sha1($value) . ')';
        }
        return '***';
    }
}
