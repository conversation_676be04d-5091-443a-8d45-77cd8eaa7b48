<?php

namespace App\Services;

use App\Events\OtpConsumed;
use App\Events\OtpGenerated;
use App\Events\OtpVerified;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class OtpService
{
    protected readonly SmsService $smsService;
    protected readonly bool $debugMode;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
        $this->debugMode = App::isLocal();
    }

    public function generateOtp(string $phoneNumber): string
    {
        $otp = (string)rand(1000, 9999);
        $expiresAt = now()->addMinutes(config('sms.otp_valid_minutes'));
        Cache::put('otp_' . $phoneNumber, $otp, $expiresAt);

        OtpGenerated::dispatch($phoneNumber, $otp, $expiresAt);

        return $otp;
    }

    public function generateAndSendOtp(string $phoneNumber): bool
    {
        $otp = $this->generateOtp($phoneNumber);
        return $this->sendOtp($phoneNumber, $otp);
    }

    public function sendOtp(string $phoneNumber, string $otp): bool
    {
        $message = "RALICOAN BATTERIES
Your one-time password is $otp. It will expire in 10 minutes.
$otp RABTRY";
        $response = $this->smsService->sendSms($phoneNumber, $message, config('sms.templates.otp'));

        if (!$response->success) {
            Log::error('Failed to send OTP via SMS to ' . $phoneNumber, [
                'response' => $response->toArray(),
            ]);
        }

        return $response->success;
    }

    public function verifyOtp(string $phoneNumber, string  $otp): bool
    {
        if ($this->debugMode && $otp === '0000') {
            return true;
        }

        $cachedOtp = Cache::get('otp_' . $phoneNumber);

        $result = $cachedOtp == $otp;

        OtpVerified::dispatch($phoneNumber, $otp, $result);

        return $result;
    }

    public function consumeOtp(string $phoneNumber, string  $otp): bool
    {
        if ($result = $this->verifyOtp($phoneNumber, $otp)) {
            Cache::forget('otp_' . $phoneNumber);
        }
        OtpConsumed::dispatch($phoneNumber, $otp, $result);
        return $result;
    }
}
