<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Redis;

class IncompleteRegistrationService
{
    private const REDIS_KEY = 'incomplete_registrations';
    
    /**
     * Add a phone number to the incomplete registrations list
     */
    public function addIncompleteRegistration(string $phoneNumber, Carbon $expiresAt): void
    {
        // Use the expiration timestamp as the score for automatic cleanup
        Redis::zadd(self::REDIS_KEY, $expiresAt->timestamp, $phoneNumber);
        
        // Set expiration for the entire sorted set (cleanup after 24 hours)
        Redis::expire(self::REDIS_KEY, 86400);
    }
    
    /**
     * Remove a phone number from the incomplete registrations list
     */
    public function removeIncompleteRegistration(string $phoneNumber): void
    {
        Redis::zrem(self::REDIS_KEY, $phoneNumber);
    }
    
    /**
     * Get all current incomplete registrations
     * Returns array of phone numbers with their expiration times
     */
    public function getIncompleteRegistrations(): array
    {
        $now = now()->timestamp;
        
        // Remove expired entries first
        Redis::zremrangebyscore(self::REDIS_KEY, 0, $now);
        
        // Get all remaining entries with scores (expiration timestamps)
        $results = Redis::zrevrangebyscore(
            self::REDIS_KEY, 
            '+inf', 
            $now + 1, 
            ['withscores' => true, 'limit' => [0, 100]]
        );
        
        $registrations = [];
        foreach ($results as $phoneNumber => $expirationTimestamp) {
            $registrations[] = [
                'phone_number' => $phoneNumber,
                'expires_at' => Carbon::createFromTimestamp($expirationTimestamp),
                'time_remaining' => $this->getTimeRemaining($expirationTimestamp),
            ];
        }
        
        return $registrations;
    }
    
    /**
     * Get count of incomplete registrations
     */
    public function getIncompleteRegistrationsCount(): int
    {
        $now = now()->timestamp;
        
        // Remove expired entries first
        Redis::zremrangebyscore(self::REDIS_KEY, 0, $now);
        
        // Count remaining entries
        return Redis::zcard(self::REDIS_KEY);
    }
    
    /**
     * Clean up expired registrations
     */
    public function cleanupExpiredRegistrations(): int
    {
        $now = now()->timestamp;
        return Redis::zremrangebyscore(self::REDIS_KEY, 0, $now);
    }
    
    /**
     * Get human-readable time remaining
     */
    private function getTimeRemaining(float $expirationTimestamp): string
    {
        $expiresAt = Carbon::createFromTimestamp($expirationTimestamp);
        $now = now();
        
        if ($expiresAt->isPast()) {
            return 'Expired';
        }
        
        $diffInMinutes = $now->diffInMinutes($expiresAt);
        
        if ($diffInMinutes < 1) {
            return 'Less than 1 minute';
        } elseif ($diffInMinutes < 60) {
            return $diffInMinutes . ' minute' . ($diffInMinutes > 1 ? 's' : '');
        } else {
            $hours = floor($diffInMinutes / 60);
            $minutes = $diffInMinutes % 60;
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . 
                   ($minutes > 0 ? ' ' . $minutes . ' minute' . ($minutes > 1 ? 's' : '') : '');
        }
    }
}
