<?php

namespace App\Policies;

use App\Enums\Roles;
use App\Models\Complaint;
use App\Models\User;

class ComplaintPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Admins can view all complaints
        if ($user->hasRole(Roles::ADMIN)) {
            return true;
        }
        
        // Staff can view complaints assigned to them
        if ($user->hasRole(Roles::STAFF)) {
            return true;
        }
        
        // Customers and Dealers can view their own complaints
        if ($user->hasRole(Roles::CUSTOMER) || $user->hasRole(Roles::DEALER)) {
            return true;
        }
        
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Complaint $complaint): bool
    {
        // Admins can view any complaint
        if ($user->hasRole(Roles::ADMIN)) {
            return true;
        }
        
        // Staff can view complaints assigned to them
        if ($user->hasRole(Roles::STAFF) && $complaint->assignee_id === $user->id) {
            return true;
        }
        
        // Customers and Dealers can view their own complaints
        if (($user->hasRole(Roles::CUSTOMER) || $user->hasRole(Roles::DEALER)) && 
            ($complaint->user_id === $user->id || $complaint->raised_by_id === $user->id)) {
            return true;
        }
        
        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Admins, Customers, and Dealers can create complaints
        return $user->hasRole(Roles::ADMIN) || 
               $user->hasRole(Roles::CUSTOMER) || 
               $user->hasRole(Roles::DEALER);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Complaint $complaint): bool
    {
        // Admins can update any complaint
        if ($user->hasRole(Roles::ADMIN)) {
            return true;
        }
        
        // Staff can update complaints assigned to them
        if ($user->hasRole(Roles::STAFF) && $complaint->assignee_id === $user->id) {
            return true;
        }
        
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Complaint $complaint): bool
    {
        // Only Admins can delete complaints
        return $user->hasRole(Roles::ADMIN);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Complaint $complaint): bool
    {
        // Only Admins can restore complaints
        return $user->hasRole(Roles::ADMIN);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Complaint $complaint): bool
    {
        // Only Admins can permanently delete complaints
        return $user->hasRole(Roles::ADMIN);
    }
    
    /**
     * Determine whether the user can assign staff to a complaint.
     */
    public function assign(User $user, Complaint $complaint): bool
    {
        // Only Admins can assign staff to complaints
        return $user->hasRole(Roles::ADMIN);
    }
}
