<?php

namespace App\Policies;

use App\Enums\Perms;
use App\Enums\Roles;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class UserPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return false;
    }

    public function createDealer(User $user): bool
    {
        return $user->hasPermission(Perms::ADD_DEALER);
    }

    public function createCustomer(User $user): bool
    {
        return $user->hasPermission(Perms::ADD_CUSTOMER);
    }

    public function createStaff(User $user): bool
    {
        return $user->hasPermission(Perms::ADD_STAFF);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        if ($model->hasRole(Roles::DEALER)) {
            return $user->hasPermission(Perms::EDIT_DEALER);
        }

        if ($model->hasRole(Roles::CUSTOMER)) {
            return $user->hasPermission(Perms::EDIT_CUSTOMER);
        }

        return $user->hasPermission(Perms::EDIT_STAFF);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        return false;
    }
}
