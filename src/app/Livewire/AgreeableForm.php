<?php

namespace App\Livewire;

use App\Data\Agreeable;
use Filament\Forms;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Livewire\Component;
use Illuminate\Contracts\View\View;

class AgreeableForm extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public array $initial = [];

    public function mount()
    {
        $this->data = [
            'terms_and_conditions' => Agreeable::latest('terms_and_conditions', 'Terms and Conditions')->content,
            'privacy_policy' => Agreeable::latest('privacy_policy', 'Privacy Policy')->content
        ];
        $this->initial = $this->data;
        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                RichEditor::make('terms_and_conditions')->required(),
                RichEditor::make('privacy_policy')->required(),
            ])
            ->statePath('data');
    }

    public function submit(): void
    {
        $data = $this->form->getState();
        if ($data['terms_and_conditions'] !== $this->initial['terms_and_conditions']) {
            Agreeable::from([
                'label' => 'Terms and Conditions',
                'id' => 'terms_and_conditions',
                'content' => $data['terms_and_conditions'],
                'createdAt' => now(),
            ])->save();
            $this->initial['terms_and_conditions'] = $data['terms_and_conditions'];
        }
        if ($data['privacy_policy'] !== $this->initial['privacy_policy']) {
            Agreeable::from([
                'label' => 'Privacy Policy',
                'id' => 'privacy_policy',
                'content' => $data['privacy_policy'],
                'createdAt' => now(),
            ])->save();
            $this->initial['privacy_policy'] = $data['privacy_policy'];
        }
        Notification::make()->title('TOC & Privacy Policy Updated')->success()->send();
    }


    public function render(): View
    {
        return view('livewire.agreeable-form');
    }
}
