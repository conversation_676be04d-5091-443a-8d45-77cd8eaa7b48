<?php

namespace App\Observers;

use App\Actions\Sms\SendWarrantyConfirmationSms;
use App\Models\Stock;

class StockObserver
{
    public function saving(Stock $stock): void
    {
        if ($stock->isDirty('warrantee_id') && $stock->warrantee_id && !$stock->warranty) {
            $stock->warranty = $stock->product->warranty;
        }
        if ($stock->isDirty('product_id') && $stock->product) {
            $stock->warranty = $stock->product->warranty;
        }
    }

    public function saved(Stock $stock): void
    {
        if ($stock->isDirty('warrantee_id')) {
            SendWarrantyConfirmationSms::dispatch($stock);
        }
    }
}
