<?php

namespace App\Observers;

use App\Data\ComplaintUpdate;
use App\Enums\ComplaintUpdateType;
use App\Models\Complaint;

class ComplaintObserver
{
    public function creating(Complaint $complaint): void
    {
        $complaint->updates[] = ComplaintUpdate::from([
            'type' => ComplaintUpdateType::Raised,
            'time' => now()
        ]);
    }

    public function saving(Complaint $complaint): void
    {
        if($complaint->isDirty('assignee_id') && $complaint->assignee_id !== null) {
            $complaint->updates[] = ComplaintUpdate::from([
                'type' => ComplaintUpdateType::Assigned,
                'time' => now()
            ]);
        }

        if ($complaint->isDirty('resolved_at') && $complaint->resolved_at !== null) {
            $complaint->updates[] = ComplaintUpdate::from([
                'type' => ComplaintUpdateType::Resolved,
                'time' => now()
            ]);
        }
    }
}
