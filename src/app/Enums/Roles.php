<?php

namespace App\Enums;

enum Roles: string
{
    case CUSTOMER = 'customer';
    case DEALER = 'dealer';
    case ADMIN = 'admin';
    case STAFF = 'staff';

    public function label(): string
    {
        return ucfirst($this->value);
    }

    public function description(): string
    {
        return match ($this) {
            self::CUSTOMER => 'Customer users can register warranty and purchase products.',
            self::DEALER => 'Dealer users can register warranties for customers. They may not purchase products, but can be assigned.',
            self::STAFF => 'Staff users have the ability to CRUD most resources and have access to the admin panel.',
            self::ADMIN => 'Administrator users can perform any action.',
        };
    }

    public function permissions(): array
    {
        return match ($this) {
            self::CUSTOMER => [Perms::REDEEM_WARRANTY],
            self::DEALER => [],
            self::STAFF => [Perms::EDIT_PRODUCTS, Perms::EDIT_CUSTOMER, Perms::EDIT_DEALER, Perms::ADD_CUSTOMER, Perms::ADD_DEALER],
            self::ADMIN => ['*', ...Perms::ALL],
        };
    }

    public static function all(): array
    {
        return [
            self::CUSTOMER,
            self::DEALER,
            self::STAFF,
            self::ADMIN
        ];
    }

    public static function allValues(): array
    {
        return [
            self::CUSTOMER->value,
            self::DEALER->value,
            self::STAFF->value,
            self::ADMIN->value,
        ];
    }
}
