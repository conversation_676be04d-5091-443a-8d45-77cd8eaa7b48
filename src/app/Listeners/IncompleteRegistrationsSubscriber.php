<?php

namespace App\Listeners;

use App\Events\OtpConsumed;
use App\Events\OtpGenerated;
use Illuminate\Events\Dispatcher;
use Illuminate\Support\Facades\Redis;

class IncompleteRegistrationsSubscriber
{
    public function handleOtpGenerated(OtpGenerated $event): void
    {
        
    }

    public function handleOtpConsumed(OtpConsumed $event): void
    {
    }

    public function subscribe(Dispatcher $events): void
    {
        $events->listen(
            OtpGenerated::class,
            [static::class, 'handleOtpGenerated']
        );

        $events->listen(
            OtpConsumed::class,
            [static::class, 'handleOtpConsumed']
        );
    }
}
