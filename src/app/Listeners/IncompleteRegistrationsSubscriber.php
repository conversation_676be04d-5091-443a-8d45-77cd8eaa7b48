<?php

namespace App\Listeners;

use App\Events\OtpConsumed;
use App\Events\OtpGenerated;
use App\Models\User;
use App\Services\IncompleteRegistrationService;
use Illuminate\Events\Dispatcher;

class IncompleteRegistrationsSubscriber
{
    public function __construct(
        private IncompleteRegistrationService $incompleteRegistrationService
    ) {}

    public function handleOtpGenerated(OtpGenerated $event): void
    {
        // Check if user already exists (registered)
        $user = User::where('phone_number', $event->phoneNumber)->first();

        // Only track as incomplete if user doesn't exist
        if (!$user) {
            $this->incompleteRegistrationService->addIncompleteRegistration(
                $event->phoneNumber,
                $event->expiresAt
            );
        }
    }

    public function handleOtpConsumed(OtpConsumed $event): void
    {
        $this->incompleteRegistrationService->removeIncompleteRegistration($event->phoneNumber);
    }

    public function subscribe(Dispatcher $events): void
    {
        $events->listen(
            OtpGenerated::class,
            [static::class, 'handleOtpGenerated']
        );

        $events->listen(
            OtpConsumed::class,
            [static::class, 'handleOtpConsumed']
        );
    }
}
