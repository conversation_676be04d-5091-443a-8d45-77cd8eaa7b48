<?php

namespace App\Providers;

use App\Listeners\IncompleteRegistrationsSubscriber;
use App\Models\Acceptance;
use App\Models\Complaint;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Stock;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {

    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Relation::enforceMorphMap([
            'user' => User::class,
            'acceptance' => Acceptance::class,
            'order' => Order::class,
            'order_item' => OrderItem::class,
            'product' => Product::class,
            'stock' => Stock::class,
            'complaint' => Complaint::class,
        ]);

        // Register event subscribers
        Event::subscribe(IncompleteRegistrationsSubscriber::class);
    }
}
