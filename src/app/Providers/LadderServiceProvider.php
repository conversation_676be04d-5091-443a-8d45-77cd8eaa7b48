<?php

namespace App\Providers;

use App\Enums\Roles;
use Illuminate\Support\ServiceProvider;
use Ladder\Ladder;

class LadderServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->configurePermissions();
    }

    /**
     * Configure the permissions that are available within the application.
     */
    protected function configurePermissions(): void
    {
        foreach (Roles::cases() as $role) {
            $role = Roles::from($role->value);

            Ladder::role($role->value, $role->label(), $role->permissions());
        }
    }
}
