services:
  app:
    image: ***************:5000/ralicoan-backend:latest
    restart: always
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    ports:
      - '${APP_PORT:-80}:80'
      - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
    environment:
      WWWUSER: '${WWWUSER}'
      IGNITION_LOCAL_SITES_PATH: '${PWD}'
    volumes:
      - 'storage:/app/storage'
    networks:
      - ralicoan
    depends_on:
      - pgsql
      - redis
  pgsql:
    image: 'postgres:15'
    restart: always
    environment:
      PGPASSWORD: '${DB_PASSWORD:-secret}'
      POSTGRES_DB: '${DB_DATABASE}'
      POSTGRES_USER: '${DB_USERNAME}'
      POSTGRES_PASSWORD: '${DB_PASSWORD:-secret}'
    volumes:
      - 'pgsql:/var/lib/postgresql/data'
    networks:
      - ralicoan
    healthcheck:
      test:
        - CMD
        - pg_isready
        - '-q'
        - '-d'
        - '${DB_DATABASE}'
        - '-U'
        - '${DB_USERNAME}'
      retries: 3
      timeout: 5s
networks:
  ralicoan:
    driver: bridge
volumes:
  storage:
    driver: local
  pgsql:
    driver: local
  redis:
    driver: local
