<script setup lang="ts">
import { ref } from 'vue';
import { RouterView, useRouter } from 'vue-router';
import loginRoutes from '~/login/routes';
import { useAuthStore } from '~/login';
import { useFavicon } from '@vueuse/core';
import { branding } from '@/utils';

const authStore = useAuthStore();

// #region Routing

const transition = ref();
const router = useRouter();

router.beforeEach((to, from) => {
	if (to.meta.transition) {
		transition.value = to.meta.transition;
		return;
	}

	const toPage = typeof to.meta.page === 'number' ? to.meta.page : 1;
	const fromPage = typeof from.meta.page === 'number' ? from.meta.page : 1;

	transition.value = toPage >= fromPage ? 'next' : 'prev';
});

router.beforeEach((to) => {
	if (to.name === 'land-page' || !to.name) {
		return;
	}

	const auth = loginRoutes().find((lr) => lr.name === to.name);
	if (auth && authStore.isLoggedIn) {
		// todo homepage
		console.log('to home page');
		return { name: 'menu' };
	} else if (!auth && !authStore.isLoggedIn) {
		console.log('to login page');
		return { name: 'login' };
	}
});

// #endregion

useFavicon(branding.logoImage.src);
</script>

<template>
	<main
		class="root w-full min-h-screen max-w-md mx-auto bg-white grid flex-1 relative z-0 overflow-x-hidden"
		style="grid-template: 'main'"
	>
		<RouterView v-slot="{ Component, route }">
			<transition :name="transition" mode="default">
				<component :is="Component" />
			</transition>
		</RouterView>
	</main>
</template>

<style>
.tsqd-open-btn-container {
	width: 16px;
	height: 16px;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
}
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.5s ease-out;
}

main.root > * {
	grid-area: main; /* Transition: make sections overlap on same cell */
	background-color: white;
	position: relative;
}

main.root > :first-child {
	z-index: 1; /* Prevent flickering on first frame when transition classes not added yet */
}

/* Transitions */

.next-leave-to {
	animation: leaveToLeft 500ms both cubic-bezier(0.165, 0.84, 0.44, 1);
	z-index: 0;
}

.next-enter-to {
	animation: enterFromRight 500ms both cubic-bezier(0.165, 0.84, 0.44, 1);
	z-index: 1;
}

.prev-leave-to {
	animation: leaveToRight 500ms both cubic-bezier(0.165, 0.84, 0.44, 1);
	z-index: 1;
}

.prev-enter-to {
	animation: enterFromLeft 500ms both cubic-bezier(0.165, 0.84, 0.44, 1);
	z-index: 0;
}

@keyframes leaveToLeft {
	from {
		transform: translateX(0);
	}
	to {
		transform: translateX(-25%);
	}
}

@keyframes enterFromLeft {
	from {
		transform: translateX(-25%);
	}
	to {
		transform: translateX(0);
	}
}

@keyframes leaveToRight {
	from {
		transform: translateX(0);
	}
	to {
		transform: translateX(100%);
	}
}

@keyframes enterFromRight {
	from {
		transform: translateX(100%);
	}
	to {
		transform: translateX(0);
	}
}
</style>
