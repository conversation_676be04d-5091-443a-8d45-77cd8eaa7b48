@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
	.btn-primary {
		@apply bg-[#0A1C7D] text-white w-full h-12 rounded-lg text-lg;
	}
	.btn-primary:disabled {
		@apply bg-[#0A1C7D] text-white w-full h-12 rounded-lg text-lg opacity-65 cursor-not-allowed;
	}
	.btn-secondary {
		@apply bg-transparent border border-[#0A1C7D] text-[#0A1C7D] w-32 h-12 rounded-lg;
	}
	.mainMneuItems {
		@apply bg-[#f0f0f0] text-[#0A1C7D] border-2 border-[#0A1C7D] w-full h-14 rounded-lg flex justify-center items-center text-lg font-semibold;
	}
	.mainMneuItems.disabled {
		@apply bg-[#f0f0f0] text-[hsl(231,25%,50%)] border-2 text-[hsl(231,25%,50%)] w-full h-14 rounded-lg flex justify-center items-center text-lg font-semibold opacity-65 cursor-not-allowed;
	}

	/* Styles for input fields that contain an element with the class 'input-bb'.
	 * Adds a bottom margin of 20px and a top margin of 10px.
	 */
	.input-field:has(.input-bb) {
		margin-bottom: 20px;
		margin-top: 10px;
	}

	.input-bb {
		border-bottom: 2px solid #7a7a7a;
		font-size: 16px;
		outline: 0;
	}

	.select-field:has(.select-bb) {
		margin-bottom: 20px;
		margin-top: 10px;
	}

	.select-bb {
		border-bottom: 2px solid #7a7a7a;
		font-size: 16px;
		outline: 0;
		min-height: 38px;
	}

	.page-title {
		@apply text-[#0A1C7D] font-semibold text-center text-2xl mb-5;
	}
}
