<?php

return [
    'api_url' => env('SMS_API_URL'),
    'provider' => env('SMS_PROVIDER', 'POWERSTEXT'),
    'token_key' => env('SMS_TOKEN_KEY'),
    'username' => env('SMS_USERNAME'),
    'password' => env('SMS_PASSWORD'),
    'sender_id' => env('SMS_SENDER_ID'),
    'route' => env('SMS_ROUTE_ID'),
    'spoof_headers' => filter_var(env('SMS_REQUIRE_SPOOFING', false), FILTER_VALIDATE_BOOLEAN),
    'templates' => [
        'otp' => env('SMS_OTP_TEMPLATE_ID'),
        'warranty_confirmation' => env('SMS_WARRANTY_CONFIRMATION_TEMPLATE_ID')
    ],
    'otp_valid_minutes' => filter_var(env('SMS_OTP_VALID_MINUTES'), FILTER_VALIDATE_INT) ?: 10,
];
