<?php
// filepath: /Users/<USER>/Projects/RA-Batteries/backend/src/tests/Feature/AuthControllerTest.php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;

class AuthControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_signup_customer_with_otp()
    {
        $response = $this->postJson('/api/auth/signup', [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone_number' => '1234567890',
            'user_type' => 'customer',
            'otp' => '123456',
        ]);

        $response->assertStatus(201)
                 ->assertJson(['message' => 'User created successfully']);
    }

    public function test_signup_staff_with_password()
    {
        $response = $this->postJson('/api/auth/signup', [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone_number' => '0987654321',
            'user_type' => 'staff',
            'password' => 'password123',
        ]);

        $response->assertStatus(201)
                 ->assertJson(['message' => 'User created successfully']);
    }

    public function test_login_staff()
    {
        $user = User::create([
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'phone_number' => '0987654321',
            'password' => Hash::make('password123'),
            'user_type' => 'staff',
        ]);

        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200)
                 ->assertJsonStructure(['access_token', 'token_type']);
    }

    public function test_login_customer_with_otp()
    {
        $user = User::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone_number' => '1234567890',
            'password' => Hash::make('default_password'),
            'user_type' => 'customer',
        ]);

        $response = $this->postJson('/api/auth/login-otp', [
            'phone_number' => '1234567890',
            'otp' => '123456',
        ]);

        $response->assertStatus(200)
                 ->assertJsonStructure(['access_token', 'token_type']);
    }

    public function test_forgot_password()
    {
        $user = User::create([
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'phone_number' => '0987654321',
            'password' => Hash::make('password123'),
            'user_type' => 'staff',
        ]);

        $response = $this->postJson('/api/auth/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
                 ->assertJson(['message' => Password::RESET_LINK_SENT]);
    }

    public function test_reset_password()
    {
        $user = User::create([
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'phone_number' => '0987654321',
            'password' => Hash::make('password123'),
            'user_type' => 'staff',
        ]);

        $token = Password::createToken($user);

        $response = $this->postJson('/api/auth/reset-password', [
            'email' => '<EMAIL>',
            'token' => $token,
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);

        $response->assertStatus(200)
                 ->assertJson(['message' => Password::PASSWORD_RESET]);
    }
}
