<?php

namespace Tests\Feature;

use App\Models\User;
use Tests\TestCase;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testIndex()
    {
        Product::factory()->count(3)->create();

        $response = $this->get('/api/products');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     '*' => ['id', 'serial_number', 'name', 'description', 'price']
                 ]);
    }

    public function testShow()
    {
        $product = Product::factory()->create();

        $response = $this->get("/api/products/{$product->id}");

        $response->assertStatus(200)
                 ->assertJson([
                     'id' => $product->id,
                     'serial_number' => $product->serial_number,
                     'name' => $product->name,
                     'description' => $product->description,
                     'price' => $product->price,
                 ]);
    }

    public function testShowNotFound()
    {
        $response = $this->get('/api/products/999');

        $response->assertStatus(404)
                 ->assertJson(['message' => 'Product not found']);
    }

    public function testStore()
    {
        $data = [
            'serial_number' => 'SN123456',
            'name' => 'Test Product',
            'description' => 'Test Description',
            'price' => 99.99,
        ];
        $this->actingAs(User::factory()->staff()->create());

        $response = $this->post('/api/products', $data);

        $response->assertStatus(201)
                 ->assertJson($data);
    }

    public function testStoreValidationError()
    {
        $this->actingAs(User::factory()->staff()->create());
        $response = $this->post('/api/products', []);

        $response->assertStatus(422)
                 ->assertJsonStructure(['errors']);
    }

    public function testUpdate()
    {
        $this->actingAs(User::factory()->staff()->create());
        $product = Product::factory()->create();
        $data = [
            'serial_number' => 'SN654321',
            'name' => 'Updated Product',
            'description' => 'Updated Description',
            'price' => 199.99,
        ];

        $response = $this->put("/api/products/{$product->id}", $data);

        $response->assertStatus(200)
                 ->assertJson($data);
    }

    public function testUpdateNotFound()
    {
        $data = [
            'serial_number' => 'SN654321',
            'name' => 'Updated Product',
            'description' => 'Updated Description',
            'price' => 199.99,
        ];

        $response = $this->put('/api/products/999', $data);

        $response->assertStatus(404)
                 ->assertJson(['message' => 'Product not found']);
    }

    public function testDestroy()
    {
        $this->actingAs(User::factory()->staff()->create());
        $product = Product::factory()->create();

        $response = $this->delete("/api/products/{$product->id}");

        $response->assertStatus(200)
                 ->assertJson(['message' => 'Product deleted successfully']);
    }

    public function testDestroyNotFound()
    {
        $response = $this->delete('/api/products/999');

        $response->assertStatus(404)
                 ->assertJson(['message' => 'Product not found']);
    }
}
