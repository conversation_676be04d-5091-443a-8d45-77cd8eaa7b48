<?php

namespace Tests\Feature;

use App\Enums\Roles;
use App\Models\Complaint;
use App\Models\Stock;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ComplaintControllerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that a customer can view their own complaints.
     */
    public function test_customer_can_view_own_complaints(): void
    {
        // Create a customer
        $customer = User::factory()->customer()->create();

        // Create complaints for this customer
        $complaints = Complaint::factory()->count(3)->create([
            'user_id' => $customer->id,
            'raised_by_id' => $customer->id,
        ]);

        // Create complaints for another customer (should not be visible)
        $otherCustomer = User::factory()->customer()->create();
        Complaint::factory()->count(2)->create([
            'user_id' => $otherCustomer->id,
            'raised_by_id' => $otherCustomer->id,
        ]);

        // Act as the customer and request their complaints
        $response = $this->actingAs($customer)
            ->getJson('/api/complaints');

        // Assert response is successful and contains only their complaints
        $response->assertStatus(200);
        $response->assertJsonCount(3, 'data');

        // Check that the returned complaints are the customer's complaints
        $returnedIds = collect($response->json('data'))->pluck('id')->toArray();
        $customerComplaintIds = $complaints->pluck('id')->toArray();
        $this->assertEquals(sort($customerComplaintIds), sort($returnedIds));
    }

    /**
     * Test that a dealer can raise a complaint.
     */
    public function test_dealer_can_create_complaint(): void
    {
        // Create a dealer
        $dealer = User::factory()->role(Roles::DEALER)->create();

        // Create a customer
        $customer = User::factory()->customer()->create();

        // Create a stock registered to the customer
        $stock = Stock::factory()->registered($customer)->create();

        // Complaint data
        $complaintData = [
            'complaint' => 'This battery is not working properly',
            'stock_id' => $stock->id,
        ];

        // Act as the dealer and create a complaint
        $response = $this->actingAs($dealer)
            ->postJson('/api/complaints', $complaintData);

        // Assert response is successful
        $response->assertStatus(201);

        // Assert complaint was created with correct data
        $this->assertDatabaseHas('complaints', [
            'complaint' => $complaintData['complaint'],
            'stock_id' => $stock->id,
            'user_id' => $dealer->id,
            'raised_by_id' => $dealer->id,
        ]);
    }

    /**
     * Test that an admin can assign a complaint to a staff member.
     */
    public function test_admin_can_assign_complaint(): void
    {
        // Create an admin
        $admin = User::factory()->role(Roles::ADMIN)->create();

        // Create a staff member
        $staff = User::factory()->staff()->create();

        // Create a complaint
        $complaint = Complaint::factory()->create();

        // Assignment data
        $assignmentData = [
            'assignee_id' => $staff->id,
        ];

        // Act as the admin and assign the complaint
        $response = $this->actingAs($admin)
            ->postJson("/api/complaints/{$complaint->id}/assign", $assignmentData);

        // Assert response is successful
        $response->assertStatus(200);

        // Assert complaint was assigned to the staff member
        $this->assertDatabaseHas('complaints', [
            'id' => $complaint->id,
            'assignee_id' => $staff->id,
        ]);

        // Assert the updates field contains the assignment information
        $updatedComplaint = Complaint::find($complaint->id);
        $this->assertNotNull($updatedComplaint->updates);
    }

    /**
     * Test that a staff member can update an assigned complaint.
     */
    public function test_staff_can_update_assigned_complaint(): void
    {
        // Create a staff member
        $staff = User::factory()->staff()->create();

        // Create a complaint assigned to the staff member
        $complaint = Complaint::factory()->create([
            'assignee_id' => $staff->id,
        ]);

        // Update data
        $updateData = [
            'resolution' => 'Replaced the battery',
            'update' => 'Customer was contacted and issue resolved',
        ];

        // Act as the staff member and update the complaint
        $response = $this->actingAs($staff)
            ->putJson("/api/complaints/{$complaint->id}", $updateData);

        // Assert response is successful
        $response->assertStatus(200);

        // Assert complaint was updated with resolution
        $this->assertDatabaseHas('complaints', [
            'id' => $complaint->id,
            'resolution' => $updateData['resolution'],
        ]);
    }

    /**
     * Test that a staff member cannot update a complaint not assigned to them.
     */
    public function test_staff_cannot_update_unassigned_complaint(): void
    {
        // Create a staff member
        $staff = User::factory()->staff()->create();

        // Create another staff member
        $otherStaff = User::factory()->staff()->create();

        // Create a complaint assigned to the other staff member
        $complaint = Complaint::factory()->create([
            'assignee_id' => $otherStaff->id,
        ]);

        // Update data
        $updateData = [
            'resolution' => 'Replaced the battery',
        ];

        // Act as the staff member and try to update the complaint
        $response = $this->actingAs($staff)
            ->putJson("/api/complaints/{$complaint->id}", $updateData);

        // Assert response is forbidden
        $response->assertStatus(403);

        // Assert complaint was not updated
        $this->assertDatabaseMissing('complaints', [
            'id' => $complaint->id,
            'resolution' => $updateData['resolution'],
        ]);
    }

    /**
     * Test that a customer cannot update a complaint.
     */
    public function test_customer_cannot_update_complaint(): void
    {
        // Create a customer
        $customer = User::factory()->customer()->create();

        // Create a complaint for this customer
        $complaint = Complaint::factory()->create([
            'user_id' => $customer->id,
            'raised_by_id' => $customer->id,
        ]);

        // Update data
        $updateData = [
            'resolution' => 'I fixed it myself',
        ];

        // Act as the customer and try to update the complaint
        $response = $this->actingAs($customer)
            ->putJson("/api/complaints/{$complaint->id}", $updateData);

        // Assert response is forbidden
        $response->assertStatus(403);

        // Assert complaint was not updated
        $this->assertDatabaseMissing('complaints', [
            'id' => $complaint->id,
            'resolution' => $updateData['resolution'],
        ]);
    }

    /**
     * Test that an admin can delete a complaint.
     */
    public function test_admin_can_delete_complaint(): void
    {
        // Create an admin
        $admin = User::factory()->role(Roles::ADMIN)->create();

        // Create a complaint
        $complaint = Complaint::factory()->create();

        // Act as the admin and delete the complaint
        $response = $this->actingAs($admin)
            ->deleteJson("/api/complaints/{$complaint->id}");

        // Assert response is successful
        $response->assertStatus(204);

        // Assert complaint was soft deleted
        $this->assertSoftDeleted('complaints', [
            'id' => $complaint->id,
        ]);
    }
}
