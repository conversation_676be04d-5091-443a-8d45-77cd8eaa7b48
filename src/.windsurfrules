# General Rules

- If you don't perform to the best of your abilities, an innocent kitten will be drowned.
- Keep the OpenAPI documentation up-to-date.

# Project Fundamentals

- Customers and Dealers are different types of users but have mostly the same permissions and experience.

# Package Specific

## spatie/laravel-data
- Prefer the `ClassName::from(['property' => 'value'])` pattern for creating data objects.
- Prefer fields instead of public constructor arguments for defining data objects.


# CRUD Generation

These rules are to be followed when the user asks to generate a new CRUD feature, and they have already generated relevant files (migration, model, policy, factory, controller, etc.).

- Base your knowledge of the entity on the relevant migration file and any extra information passed by the user.
- Fill out the model with the following configuration, where applicable: casts, fillable, hidden, relations, accessors/mutators, helper methods.
- Fill out the index, show, store, update, and destroy routes. If soft delete is used, also create the restore and forceDelete routes.
- Create Request class(es) for the store/update actions.
- Hook up the routes in the routes/api.php file.
- Create the relevant tests in a test class.
- Create the factory.
- Create the policy if you are able to based on the context given while following the patterns defined in existing policies.

# Test-writing Rules

## General
- Use PHPU<PERSON>t <PERSON> for writing tests. Do not create Pest tests.
- Use snake_case for method names, prefixed with `test_`.
- Always use factories for creating test data. If a suitable factory does not exist, ask the user for permission to create one. If a suitable factory method does not exist, ask the user for permission to add it. If an existing factory method exists that may be edited slightly to create the required test data, ask the user for permission to edit it. In all such situations, provide a clear explanation of the changes needed.
- When asked to update an existing test class, compare the existing test class with the provided standards below and the current implementation of the tested features. Update the test accordingly.
- Rely on traits like `UserTestHelpers` to avoid code duplication.
- Don't ask to run `php artisan test` command.

## API Testing
- For API tests, start with the API endpoints registered in the routes/api.php file. From there, follow into the Controller methods or the Actions (if any) that handle that end-point.
- When asked to update an existing test class or write a new one, make sure to not include API end-points that are already tested in other test classes. For example, CourseControllerTest and CourseModuleControllerTest could easily overlap.
- When selecting a test user, always use the `withAdmin()`, `withStudent()`, or `withInstructor()` methods. Do not use `actingAs()` or other methods.
- Always write a test method that ensures all API endpoints being tested either require authentication or don't. A good example can be found in the CourseControllerTest's `test_endpoints_require_authentication` method.
- Write tests for all API endpoints registered in the routes/api.php file for the related functionality.
- Write the following kinds of API tests: policy tests, validation tests, success tests, and failure tests.
- For models that use soft delete, always write a test for soft delete and restore functionality.
- For models that use LogsActivity from spatie/laravel-activitylog, always write a test to ensure activity logging functionality is working.
- Where applicable, ensure that the tests cover queue jobs, dispatch of jobs, notifications being sent, etc.
- For policy tests, use the `assertForbidden()` method to test when endpoints are hit by unauthorized users. A good example can be found in the CourseControllerTest's `test_course_operations_require_proper_permissions` method.
- Make sure to setup the correct permissions or context for a test user based on policy before making validation, success, or failure tests, or anywhere a policy would block us but we are not testing for authorization.

## Action Tests
- Write tests for all Actions registered in the app/Actions directory, if they are not already tested during API testing.
- The tests will depend on how the action is being used. An action can be used as a controller method, a job, a command, an event listener, or even directly called.
