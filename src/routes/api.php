<?php

use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CustomerStockController;
use App\Http\Controllers\ComplaintController;

Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']); // For staff and dealers
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('/reset-password', [AuthController::class, 'resetPassword']);
    Route::post('/login-otp', [AuthController::class, 'loginWithOtp']); // For customers
    Route::post('/send-otp', [AuthController::class, 'sendOtp']); // Route for sending OTP
    Route::delete('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');
});

Route::prefix('products')->group(function () {
    Route::get('/', [ProductController::class, 'index']);
    Route::get('/{id}', [ProductController::class, 'show']);
});

Route::get('/agreeables', \App\Actions\GetAgreeables::class);

Route::middleware('auth:sanctum')->group(function () {
    // Customer-specific Routes

    Route::prefix('/customer')->group(function () {
        // for warranty redemption
        Route::prefix('/stock')->group(function () {
            Route::get('/', [CustomerStockController::class, 'index']);
            Route::get('/{stock}', [CustomerStockController::class, 'show']);
            Route::get('/find/{serial}', [CustomerStockController::class, 'find']);
            Route::post('/redeem/{serial}', [CustomerStockController::class, 'redeem']);
        });
    });

    // Dealer-specific routes
    Route::prefix('/dealer')->group(function () {
        Route::post('activate', [\App\Http\Controllers\DealerController::class, 'activate']);
    });

    //user
    Route::get('/users/{user}', [UserController::class, 'show']);
    Route::patch('/users/update-name', [UserController::class, 'updateName']);
    
    // Complaints routes
    Route::prefix('/complaints')->group(function () {
        Route::get('/', [ComplaintController::class, 'index']);
        Route::post('/', [ComplaintController::class, 'store']);
        Route::get('/{complaint}', [ComplaintController::class, 'show']);
        Route::put('/{complaint}', [ComplaintController::class, 'update']);
        Route::delete('/{complaint}', [ComplaintController::class, 'destroy']);
        Route::post('/{complaint}/assign', [ComplaintController::class, 'assign']);
    });
});
