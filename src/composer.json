{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-rdkafka": "*", "aka/laravel-database-notification": "dev-master", "eneadm/ladder": "^1.2", "filament/filament": "^3.2", "firebase/php-jwt": "^6.10", "laravel/framework": "^11.31", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "mateusjunges/laravel-kafka": "^2.4", "owen-it/laravel-auditing": "^13.6", "spatie/laravel-data": "^4"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.40", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1", "wulfheart/laravel-actions-ide-helper": "^0.10.0"}, "repositories": [{"type": "git", "url": "https://git.akaal.biz/aka/laravel-database-notification.git"}], "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan ide-helper:custom"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}, "gitlab-domains": ["git.akaal.biz"]}, "minimum-stability": "dev", "prefer-stable": true}