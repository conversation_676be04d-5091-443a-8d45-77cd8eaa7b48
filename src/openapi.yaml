openapi: 3.1.0
info:
  title: R A Batteries API
  description: API for managing warranty status of customers and dealers.
  version: 1.0.0
servers:
  - url: https://api.rabatteries.in/api
    description: Production server
  - url: http://localhost:80/api
    description: Development server
security:
  - bearerAuth: [ ]
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    User:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          format: int64
        name:
          type: [ string, "null" ]
        email:
          type: [ string, "null" ]
          format: email
        phone_number:
          type: [ string, "null" ]
        dealership_code:
          type: [ string, "null" ]
        dealership_activated_at:
          type: [ string, "null" ]
        email_verified_at:
          type: [ string, "null" ]
          format: date-time
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        # Relations
        stocks:
          type: array
          items:
            $ref: "#/components/schemas/Stock"
    Product:
      type: object
      required:
        - id
        - name
        - price
        - status
        - description
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        price:
          type: number
          format: decimal
        description:
          type: string
        specifications:
          type: object
          default: { }
        warranty:
          oneOf:
            - $ref: "#/components/schemas/WarrantySpec"
            - type: "null"
        status:
          type: string
          enum: [ active, inactive ]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        # Relations
        stocks:
          type: array
          items:
            $ref: "#/components/schemas/Stock"
    Stock:
      type: object
      required:
        - id
        - serial_number
        - product_id
        - created_at
        - updated_at
      properties:
        id:
          type: integer
          format: int64
        serial_number:
          type: string
        product_id:
          type: integer
          format: int64
        warrantee_id:
          type: [ integer, "null" ]
          format: int64
        warranty_start:
          type: [ string, "null" ]
          format: date-time
        warranty_end:
          type: [ string, "null" ]
          format: date-time
        warranty:
          oneOf:
            - $ref: "#/components/schemas/WarrantySpec"
            - type: "null"
        order_item_id:
          type: [ integer, "null" ]
          format: int64
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        deleted_at:
          type: string
        # Relations
        product:
          $ref: "#/components/schemas/Product"
        warrantee:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/User"
        order_item:
          type: array
          items:
            $ref: "#/components/schemas/OrderItem"
    Order:
      type: object
      required:
        - id
        - user_id
        - order_placed_at
        - address
        - pincode
        - phone_number
        - created_at
        - updated_at
        - deleted_at
      properties:
        id:
          type: integer
          format: int64
        user_id:
          type: integer
          format: int64
        order_placed_at:
          type: string
          format: date-time
        address:
          type: string
        pincode:
          type: string
        phone_number:
          type: string
        email:
          type: [ string, "null" ]
        bill_number:
          type: [ string, "null" ]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
        deleted_at:
          type: string
        order_items:
          type: array
          items:
            $ref: "#/components/schemas/OrderItem"
    OrderItem:
      type: object
      required:
        - id
        - product_id
        - order_id
        - quantity
        - created_at
        - updated_at
      properties:
        id:
          type: integer
          format: int64
        product_id:
          type: integer
          format: int64
        order_id:
          type: integer
          format: int64
        quantity:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        # Relations
        product:
          $ref: "#/components/schemas/Product"
        stocks:
          type: array
          items:
            $ref: "#/components/schemas/Stock"
    Complaint:
      type: object
      required:
        - id
        - complaint
        - stock_id
        - user_id
        - raised_by_id
        - created_at
        - updated_at
      properties:
        id:
          type: integer
          format: int64
        complaint:
          type: string
          description: The complaint text
        stock_id:
          type: integer
          format: int64
        user_id:
          type: integer
          format: int64
        raised_by_id:
          type: integer
          format: int64
        assignee_id:
          type: [ integer, "null" ]
          format: int64
        resolution:
          type: [ string, "null" ]
        resolved_at:
          type: [ string, "null" ]
          format: date-time
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        deleted_at:
          type: [ string, "null" ]
          format: date-time
        # Relations
        stock:
          $ref: "#/components/schemas/Stock"
        user:
          $ref: "#/components/schemas/UserMinimal"
        assignee:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/UserMinimal"
        raisedBy:
          $ref: "#/components/schemas/UserMinimal"
        updates:
          type: array
          items:
            $ref: "#/components/schemas/ComplaintUpdate"
    ComplaintUpdate:
      type: object
      required:
        - type
        - time
      properties:
        type:
          type: string
          enum: [ raised, assigned, resolved ]
          description: The type of update
        time:
          type: string
          format: date-time
          description: When the update occurred
    UserMinimal:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
    WarrantySpec:
      type: object
      required:
        - description
        - duration
        - extension
        - leeway
      properties:
        description:
          type: string
          description: Description of the warranty
        duration:
          type: integer
          description: Duration of warranty (in months)
        extension:
          type: integer
          description: Extension of warranty (in months)
          default: 0
        leeway:
          type: integer
          description: Leeway of warranty for sales (in months)
          default: 0
    Acceptance:
      type: object
      required:
        - id
      properties:
        id:
          type: number
          format: int64
        user_id:
          type: number
          format: int64
        ip_address:
          type: string
        user_agent:
          type: string
        document:
          type: string
        document_version:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    Agreeable:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        content:
          type: string
        label:
          type: string
        created_at:
          type: string
          format: date-time
    Error:
      type: object
      properties:
        message:
          type: string
        errors:
          type: object
    ValidationError:
      type: object
      properties:
        message:
          type: string
          example: "The given data was invalid."
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            email: [ "The email field is required." ]
            password: [ "The password field is required." ]
    NotFoundError:
      type: object
      properties:
        message:
          type: string
          example: "Resource not found"
    UnauthorizedError:
      type: object
      properties:
        message:
          type: string
          example: "Unauthenticated."
    ForbiddenError:
      type: object
      properties:
        message:
          type: string
          example: "You are not authorized to perform this action."
  responses:
    ValidationError:
      description: Validation failed
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ValidationError"
    NotFoundError:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/NotFoundError"
    UnauthorizedError:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UnauthorizedError"
    ForbiddenError:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ForbiddenError"
    BadRequestError:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ValidationError"
paths:
  /api/auth/login:
    post:
      operationId: loginUser
      summary: Login for staff and dealers
      tags: [ Authentication ]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  format: password
      responses:
        "200":
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "422":
          $ref: "#/components/responses/ValidationError"
  /api/auth/login-otp:
    post:
      operationId: loginWithOtp
      summary: Login with OTP for customers
      tags: [ Authentication ]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone_number
                - otp
                - app_type
              properties:
                phone_number:
                  type: string
                otp:
                  type: string
                app_type:
                  type: string
      responses:
        "200":
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                required:
                  - access_token
                  - user
                properties:
                  access_token:
                    type: string
                  user:
                    $ref: "#/components/schemas/User"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "422":
          $ref: "#/components/responses/ValidationError"
  /api/auth/send-otp:
    post:
      operationId: sendOtp
      summary: Send OTP for customer login
      tags: [ Authentication ]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone_number
              additionalProperties: true
              properties:
                phone_number:
                  type: string
      responses:
        "200":
          description: OTP sent successfully
        "422":
          $ref: "#/components/responses/ValidationError"
  /api/auth/logout:
    delete:
      operationId: logoutUser
      summary: Logout user
      tags: [ Authentication ]
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: Logged out successfully
        "401":
          $ref: "#/components/responses/UnauthorizedError"
  /api/products:
    get:
      operationId: listProducts
      summary: List all products
      tags: [ Products ]
      responses:
        "200":
          description: List of products
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Product"
  /api/products/{id}:
    get:
      operationId: getProduct
      summary: Get product details
      tags: [ Products ]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Product"
        "404":
          $ref: "#/components/responses/NotFoundError"
  /api/complaints:
    get:
      operationId: listComplaints
      summary: List complaints based on user role
      tags: [ Complaints ]
      security:
        - bearerAuth: [ ]
      parameters:
        - name: resolved
          in: query
          required: false
          schema:
            type: boolean
          description: Filter by resolution status
        - name: stock_id
          in: query
          required: false
          schema:
            type: integer
          description: Filter by stock ID
      responses:
        "200":
          description: List of complaints
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/Complaint"
                  links:
                    type: object
                  meta:
                    type: object
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
    post:
      operationId: createComplaint
      summary: Create a new complaint
      tags: [ Complaints ]
      security:
        - bearerAuth: [ ]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - complaint
                - stock_id
                - user_id
              properties:
                complaint:
                  type: string
                stock_id:
                  type: integer
                user_id:
                  type: integer
                raised_by_id:
                  type: integer
      responses:
        "201":
          description: Complaint created successfully
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                properties:
                  data:
                    $ref: "#/components/schemas/Complaint"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "422":
          $ref: "#/components/responses/ValidationError"
  /api/complaints/{id}:
    get:
      operationId: getComplaint
      summary: Get complaint details
      tags: [ Complaints ]
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Complaint details
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                properties:
                  data:
                    $ref: "#/components/schemas/Complaint"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
    put:
      operationId: updateComplaint
      summary: Update a complaint
      tags: [ Complaints ]
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                complaint:
                  type: string
                resolution:
                  type: string
                resolved_at:
                  type: string
                  format: date-time
                assignee_id:
                  type: integer
      responses:
        "200":
          description: Complaint updated successfully
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                properties:
                  data:
                    $ref: "#/components/schemas/Complaint"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
        "422":
          $ref: "#/components/responses/ValidationError"
    delete:
      operationId: deleteComplaint
      summary: Delete a complaint
      tags: [ Complaints ]
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "204":
          description: Complaint deleted successfully
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
  /api/complaints/{id}/assign:
    post:
      operationId: assignComplaint
      summary: Assign a complaint to a staff member
      tags: [ Complaints ]
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - assignee_id
              properties:
                assignee_id:
                  type: integer
      responses:
        "200":
          description: Complaint assigned successfully
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                properties:
                  data:
                    $ref: "#/components/schemas/Complaint"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
        "422":
          $ref: "#/components/responses/ValidationError"
  /api/customer/stock:
    get:
      operationId: listCustomerStocks
      summary: List customer's stocks
      tags: [ Customer Stock ]
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: List of stocks
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/Stock"
                  links:
                    type: object
                  meta:
                    type: object
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
  /api/customer/stock/{stock}:
    get:
      operationId: getCustomerStock
      summary: Get stock details
      tags: [ Customer Stock ]
      security:
        - bearerAuth: [ ]
      parameters:
        - name: stock
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Stock details
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                properties:
                  data:
                    $ref: "#/components/schemas/Stock"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
  /api/customer/stock/find/{serial}:
    get:
      operationId: findStockBySerial
      summary: Find stock by serial number
      tags: [ Customer Stock ]
      security:
        - bearerAuth: [ ]
      parameters:
        - name: serial
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Stock details
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                properties:
                  data:
                    $ref: "#/components/schemas/Stock"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
  /api/customer/stock/redeem/{serial}:
    post:
      operationId: redeemWarranty
      summary: Redeem warranty for a stock
      tags: [ Customer Stock ]
      security:
        - bearerAuth: [ ]
      parameters:
        - name: serial
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Warranty redeemed successfully
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                properties:
                  data:
                    $ref: "#/components/schemas/Stock"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
        "422":
          $ref: "#/components/responses/ValidationError"
  /api/users/{user}:
    get:
      operationId: getUser
      summary: Get user details
      tags: [ Users ]
      security:
        - bearerAuth: [ ]
      parameters:
        - name: user
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: User details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
  /api/users/update-name:
    patch:
      operationId: updateUserName
      summary: Update authenticated user's name
      tags: [ Users ]
      security:
        - bearerAuth: [ ]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                  maxLength: 255
      responses:
        "200":
          description: Name updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Name updated successfully"
                  user:
                    $ref: "#/components/schemas/User"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "422":
          $ref: "#/components/responses/ValidationError"
  /api/agreeables:
    get:
      operationId: getAgreeables
      summary: Get agreeables
      tags: [ Agreeables ]
      responses:
        "200":
          description: Agreeables details
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Agreeable"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
  /api/dealer/activate:
    post:
      operationId: activateDealership
      summary: Activate dealership
      tags: [ Dealership ]
      security:
        - bearerAuth: [ ]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dealership_activation_code
              properties:
                dealership_activation_code:
                  type: string
      responses:
        200:
          description: Dealership activated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
        "400":
          $ref: "#/components/responses/BadRequestError"
        "401":
          $ref: "#/components/responses/UnauthorizedError"
        "403":
          $ref: "#/components/responses/ForbiddenError"
        "404":
          $ref: "#/components/responses/NotFoundError"
        "422":
          $ref: "#/components/responses/ValidationError"
