<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('complaints', function (Blueprint $table) {
            $table->id();
            $table->text('complaint');
            $table->text('resolution')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->jsonb('updates')->nullable();
            $table->foreignIdFor(\App\Models\Stock::class)->constrained();
            $table->foreignIdFor(\App\Models\User::class)->constrained();
            $table->foreignIdFor(\App\Models\User::class, 'assignee_id')->nullable()->constrained('users');
            $table->foreignIdFor(\App\Models\User::class, 'raised_by_id')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('complaints');
    }
};
