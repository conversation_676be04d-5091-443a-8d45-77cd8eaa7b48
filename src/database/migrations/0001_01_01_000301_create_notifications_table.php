<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('type');
            $table->morphs('notifiable');
            $table->string('tag')->nullable()->index('tag_index');
            $table->smallInteger('priority')->default(0);
            $table->boolean('tag_ignores_notifiable_id')
                ->default(false)
                ->comment('If notification is tagged, the tag is unique per notifiable. If this is true, the tag is unique per notifiable type.');
            $table->boolean('deletable')->default(false)->index();
            $table->string('data');
            $table->timestamp('read_at')->nullable()->index();
            $table->timestamp('expires_at')->nullable()->index();
            $table->timestamps();

            $table->index('priority', 'created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');
    }
};
