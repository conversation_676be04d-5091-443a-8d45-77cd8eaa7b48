<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'order_date' => $this->faker->dateTimeBetween('-5 months', 'now'),
            'status' => 'paid',
            'total_amount' => $this->faker->randomFloat(2, 0, 1000),
        ];
    }

    public function singleProduct(Product $product, string $serialNumber): OrderFactory|Factory
    {
        return $this->afterCreating(function (Order $order) use ($product, $serialNumber) {
            $item = OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'quantity' => 1,
                'serial_number' => $serialNumber,
                'amount' => $product->price,
                'warranty' => $product->warranty,
            ]);
        });
    }
}
