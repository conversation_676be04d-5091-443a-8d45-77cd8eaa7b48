<?php

namespace Database\Factories;

use App\Data\WarrantySpec;
use App\Enums\ProductStatus;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company(),
            'status' => ProductStatus::PUBLISHED,
            'description' => fake()->text(),
            'price' => fake()->randomFloat(2, 0, 1000),
            'warranty' => WarrantySpec::from([
                'description' => fake()->text(),
                'leeway' => fake()->numberBetween(1, 3),
                'duration' => fake()->numberBetween(6, 24),
                'extension' => fake()->numberBetween(0, 6),
            ])
        ];
    }
}
