<?php

namespace Database\Factories;

use App\Data\WarrantySpec;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Stock>
 */
class StockFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'serial_number' => fake()->unique()->regexify('[A-Z]{2}[0-9]{8}'),
            'product_id' => Product::factory(),
            'dealer_bill_number' => fake()->unique()->regexify('BILL-[0-9]{6}'),
            'warranty' => new WarrantySpec(
                duration: fake()->numberBetween(6, 24),
                leeway: fake()->numberBetween(1, 3)
            ),
        ];
    }
    
    /**
     * Indicate that the stock has been registered to a customer.
     */
    public function registered(User $user = null): static
    {
        $user = $user ?? User::factory()->customer()->create();
        $warrantyStart = now()->subMonths(fake()->numberBetween(1, 6));
        
        return $this->state(function (array $attributes) use ($user, $warrantyStart) {
            $warrantySpec = $attributes['warranty'] ?? new WarrantySpec(
                duration: 12,
                leeway: 1
            );
            
            return [
                'warrantee_id' => $user->id,
                'warranty_start' => $warrantyStart,
                'warranty_end' => $warrantyStart->clone()->addMonths($warrantySpec->duration),
            ];
        });
    }
}
