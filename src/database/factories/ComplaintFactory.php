<?php

namespace Database\Factories;

use App\Data\ComplaintUpdate;
use App\Enums\ComplaintUpdateType;
use App\Enums\Roles;
use App\Models\Stock;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Spatie\LaravelData\DataCollection;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Complaint>
 */
class ComplaintFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $user = User::factory()->customer()->create();
        $stock = Stock::factory()->registered($user)->create();

        return [
            'complaint' => fake()->paragraph(),
            'stock_id' => $stock->id,
            'user_id' => $user->id,
            'raised_by_id' => $user->id,
        ];
    }

    /**
     * Indicate that the complaint has been resolved.
     */
    public function resolved(): static
    {
        return $this->state(function (array $attributes) {
            $resolvedAt = Carbon::now()->subDays(fake()->numberBetween(1, 5));

            return [
                'resolution' => fake()->paragraph(),
                'resolved_at' => $resolvedAt,
            ];
        });
    }

    /**
     * Indicate that the complaint has been assigned to a staff member.
     */
    public function assigned(): static
    {
        return $this->state(function (array $attributes) {
            $staff = User::factory()->staff()->create();

            return [
                'assignee_id' => $staff->id,
            ];
        });
    }

    /**
     * Indicate that the complaint was raised by a dealer.
     */
    public function raisedByDealer(): static
    {
        return $this->state(function (array $attributes) {
            $dealer = User::factory()->role(Roles::DEALER)->create();

            return [
                'raised_by_id' => $dealer->id,
            ];
        });
    }
}
