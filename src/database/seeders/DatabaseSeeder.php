<?php

namespace Database\Seeders;

use App\Actions\RecreateDatabase;
use App\Data\WarrantySpec;
use App\Enums\Roles;
use App\Models\Order;
use App\Models\Product;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            UserSeeder::class
        ]);

        if (App::isLocal()) {
            app(RecreateDatabase::class)->execute();
        }
    }
}
