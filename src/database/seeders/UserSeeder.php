<?php

namespace Database\Seeders;

use App\Enums\Roles;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!App::isLocal()) {
            $admin = User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone_number' => '0000000000',
            ]);
            $admin->assignRole(Roles::ADMIN);
            return;
        }

        $admin = User::create([
            'name' => 'Admin 1',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone_number' => '0000000000',
        ]);
        $admin->assignRole(Roles::ADMIN);

        $staff = User::create([
            'name' => 'Staff 1',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone_number' => '1111111111',
        ]);
        $staff->assignRole(Roles::STAFF);

        $dealer = User::create([
            'name' => 'Dealer 1',
            'email' => '<EMAIL>',
            'dealership_code' => 'D1',
            'password' => Hash::make('password'),
            'phone_number' => '2222222222',
        ]);
        $dealer->assignRole(Roles::DEALER);

        $customer = User::create([
            'name' => 'Customer 1',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone_number' => '3333333333',
        ]);
        $customer->assignRole(Roles::CUSTOMER);
    }
}
