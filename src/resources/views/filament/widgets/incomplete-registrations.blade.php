<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Incomplete Registrations
        </x-slot>

        <x-slot name="description">
            Users who received OTP but haven't completed registration ({{ $totalCount }} total)
        </x-slot>

        @if(count($incompleteRegistrations) > 0)
            <div class="space-y-3">
                @foreach($incompleteRegistrations as $registration)
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                    +91 {{ $registration['phone_number'] }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    OTP expires: {{ $registration['expires_at']->format('M j, Y g:i A') }}
                                </p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {{ $registration['expires_at']->isPast() ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' }}">
                                {{ $registration['time_remaining'] }}
                            </span>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-6">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    No incomplete registrations at the moment
                </p>
            </div>
        @endif

        @if(count($incompleteRegistrations) > 0)
            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    <strong>Note:</strong> These are users who received an OTP but haven't completed the registration process.
                    Expired OTPs are automatically cleaned up.
                </p>
            </div>
        @endif
    </x-filament::section>
</x-filament-widgets::widget>
