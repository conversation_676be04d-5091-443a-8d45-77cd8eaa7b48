# SMS Gateway API Documentation
**SweetSMS**

## Send

### Endpoint:
`http://173.45.76.227/send.aspx?username={env.smsUsername}&pass={env.smsPassword}&route={env.smsRoute}&senderid={env.smsSenderId}&numbers={phone-number}&message={message}&templateid={env.smsSignUpTemplateId}`

### Parameters:
- username: Account Username at SMS Gateway
- password: Account password
- route: SMS Gateway Route Id
- senderid: SenderId provided by SMS Gateway
- numbers: comma separated mobile numbers (10 or 12 digit only)
- templateid: DLT approved template id.

### Return Status 
Status will be returned as | delimited data string with the following possible values
Return: Status|Units|smsid
Possible values: 
- Status:
    - 1: Success
    - 2: Invalid Credentials
    - 3: Insufficient Balance
    - 4: Unknown Error
    - 5: Invalid senderid
    - 6: Invalid route Id
    - 7: Submission Error
    - 10: Template id missing
- Units:
    Consumed units (only returned with status is 1)
- smsid:
    Unique delivery id for delivery status tracking (only returned when status is 1)

## Check Delivery Status

### Endpoint:
`http://173.45.76.227/status.aspx?username={env.smsUsername}&pass{env.smsPassword}=&msgid={smsid}`

### Parameters:
    - username: Account Username at SMS Gateway
    - password: Account password
    - smsid: SMS id returned after submission

### Return Status:
Returns JSON formated devliery status as per below example:
```{
    "Status": true,
    "Message": "Success",
    "Response": [
        {
            "Mobile":"xxxxxxxxxx",
            "DeliveryStatus":"DELIVRD"
        },
        {
            "Mobile":"9xxxxxxxx6",
            "DeliveryStatus":"Unknown subscriber"
        },
        {
            "Mobile":"9xxxxxxxx6",
            "DeliveryStatus":"Abort"
        },
        {
            "Mobile":"9xxxxxxxx6",
            "DeliveryStatus":"Facility not supported"
        },
        {
            "Mobile":"9xxxxxxxx6",
            "DeliveryStatus":"UNDELIV"
        }
    ]
}```